# Application Configuration
spring.application.name=pr-analyzer
server.port=8080

# GitHub API Configuration
github.token=${GITHUB_TOKEN:your-github-token-here}
github.api.base-url=https://api.github.com

# OpenAI API Configuration
openai.api.key=${OPENAI_API_KEY:your-openai-api-key-here}
openai.api.base-url=https://api.openai.com/v1

# Logging Configuration
logging.level.com.reflexai=DEBUG
logging.level.org.springframework.web=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# HTTP Client Configuration
spring.webflux.timeout=30s
