package com.reflexai.pranalyzer.service;

import com.reflexai.pranalyzer.model.PRData;
import com.reflexai.pranalyzer.model.PRResponse;
import com.reflexai.pranalyzer.model.PRFile;
import com.reflexai.pranalyzer.util.PRUrlParser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;

/**
 * Service for interacting with GitHub API
 */
@Service
public class GitHubService {
    
    @Value("${github.token}")
    private String githubToken;
    
    private final RestTemplate restTemplate;
    
    public GitHubService() {
        this.restTemplate = new RestTemplate();
    }
    
    public PRData analyzePR(String prUrl) {
        PRUrlParser.ParsedUrl parsed = PRUrlParser.parse(prUrl);
        
        // Get PR details
        String prApiUrl = String.format("https://api.github.com/repos/%s/%s/pulls/%s", 
            parsed.getOwner(), parsed.getRepo(), parsed.getPrNumber());
            
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "token " + githubToken);
        headers.set("Accept", "application/vnd.github.v3+json");
        
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        ResponseEntity<PRResponse> prResponse = restTemplate.exchange(
            prApiUrl, HttpMethod.GET, entity, PRResponse.class);
            
        // Get file changes
        String filesUrl = prApiUrl + "/files";
        ResponseEntity<PRFile[]> filesResponse = restTemplate.exchange(
            filesUrl, HttpMethod.GET, entity, PRFile[].class);
            
        return PRData.builder()
            .title(prResponse.getBody().getTitle())
            .description(prResponse.getBody().getBody())
            .files(Arrays.asList(filesResponse.getBody()))
            .build();
    }
}
