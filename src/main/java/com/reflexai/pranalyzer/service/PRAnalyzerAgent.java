package com.reflexai.pranalyzer.service;

import com.reflexai.pranalyzer.model.AgentResponse;
import com.reflexai.pranalyzer.model.PRData;
import com.reflexai.pranalyzer.model.CodePatterns;
import com.reflexai.pranalyzer.util.PatternExtractor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Main service for analyzing PRs and generating similar code
 */
@Service
public class PRAnalyzerAgent {

    @Autowired
    private GitHubService gitHubService;

    @Autowired
    private OpenAIService openAIService;

    @Autowired
    private PatternExtractor patternExtractor;

    public AgentResponse analyzePRAndGenerate(String prUrl, String requirements) {
        try {
            // Step 1: Analyze PR
            PRData prData = gitHubService.analyzePR(prUrl);

            // Step 2: Extract patterns
            CodePatterns patterns = patternExtractor.extractPatterns(prData);

            // Step 3: Generate similar code
            String generatedCode = openAIService.generateSimilarCode(patterns, requirements);

            return AgentResponse.builder()
                .prAnalysis(prData)
                .extractedPatterns(patterns)
                .generatedCode(generatedCode)
                .build();

        } catch (Exception e) {
            throw new RuntimeException("Failed to analyze PR and generate code", e);
        }
    }
}
