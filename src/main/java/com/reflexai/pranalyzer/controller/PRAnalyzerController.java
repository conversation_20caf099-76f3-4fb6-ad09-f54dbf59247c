package com.reflexai.pranalyzer.controller;

import com.reflexai.pranalyzer.model.AnalysisRequest;
import com.reflexai.pranalyzer.model.AgentResponse;
import com.reflexai.pranalyzer.service.PRAnalyzerAgent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * REST Controller for PR Analysis and Code Generation
 */
@RestController
@RequestMapping("/api/pr-analyzer")
public class PRAnalyzerController {
    
    @Autowired
    private PRAnalyzerAgent agent;
    
    @PostMapping("/analyze-and-generate")
    public ResponseEntity<AgentResponse> analyzeAndGenerate(@Valid @RequestBody AnalysisRequest request) {
        try {
            AgentResponse response = agent.analyzePRAndGenerate(
                request.getPrUrl(), 
                request.getRequirements()
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(AgentResponse.error(e.getMessage()));
        }
    }
    
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("PR Analyzer is running");
    }
}
