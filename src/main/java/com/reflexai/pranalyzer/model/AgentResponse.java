package com.reflexai.pranalyzer.model;

import lombok.Builder;
import lombok.Data;

/**
 * Response model for PR analysis and code generation
 */
@Data
@Builder
public class AgentResponse {
    
    private PRData prAnalysis;
    private CodePatterns extractedPatterns;
    private String generatedCode;
    private boolean success;
    private String errorMessage;
    
    public static AgentResponse error(String message) {
        return AgentResponse.builder()
            .success(false)
            .errorMessage(message)
            .build();
    }
}
