package com.reflexai.pranalyzer.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Model representing extracted code patterns
 */
@Data
public class CodePatterns {
    
    private List<String> methodPatterns = new ArrayList<>();
    private List<String> variablePatterns = new ArrayList<>();
    private List<String> logicPatterns = new ArrayList<>();
    private List<String> structuralPatterns = new ArrayList<>();
    
    public void addMethodPattern(String pattern) {
        methodPatterns.add(pattern);
    }
    
    public void addVariablePattern(String pattern) {
        variablePatterns.add(pattern);
    }
    
    public void addLogicPattern(String pattern) {
        logicPatterns.add(pattern);
    }
    
    public void addStructuralPattern(String pattern) {
        structuralPatterns.add(pattern);
    }
}
