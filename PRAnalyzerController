@RestController
@RequestMapping("/api/pr-analyzer")
public class PRAnalyzerController {
    
    @Autowired
    private PRAnalyzerAgent agent;
    
    @PostMapping("/analyze-and-generate")
    public ResponseEntity<AgentResponse> analyzeAndGenerate(@RequestBody AnalysisRequest request) {
        try {
            AgentResponse response = agent.analyzePRAndGenerate(
                request.getPrUrl(), 
                request.getRequirements()
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(AgentResponse.error(e.getMessage()));
        }
    }
}