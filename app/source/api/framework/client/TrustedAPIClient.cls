<?php

class TrustedAPIClient
{
    const HTTP_RESPONSE = 'response';
    const HTTP_CODE = 'httpCode';
    const HTTP_HEADERS = "headers";

    const HTTP_NO_CONTENT = 204;

    // TODO: Objectify the logic based on $operation into APIOperation classes
    const METHOD = "method";
    const CONVERT_TO_API = "convertToAPI";
    const CONVERT_FROM_API_RULE = "convertFromAPIRule";
    const SKIP_CONVERT_ON_ERROR_CODE = "skipConvertOnError";
    const CONVERT_RESULT_TO_API = "convertResultToAPI";
    const RETURN_RESULT_ONLY = "returnResultOnly";
    const RETURN_RESPONSE_ONLY = "returnResponseOnly";
    const RETURN_ALL = "returnAll";
    const RETURN_VOID = "returnVoid";

    const API_OPERATION_MAP = [
        APIConstants::API_OPERATION_READ                => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_GET,
                                                             self::CONVERT_TO_API             => true,
                                                             self::CONVERT_FROM_API_RULE      => self::CONVERT_RESULT_TO_API,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_OPTIONS             => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_OPTIONS,
                                                             self::CONVERT_TO_API             => true,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_VOID,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        // do we really need it?
        APIConstants::API_OPERATION_CREATE              => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_POST,
                                                             self::CONVERT_TO_API             => true,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_RESULT_ONLY,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_PATCH               => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_PATCH,
                                                             self::CONVERT_TO_API             => true,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_RESULT_ONLY,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_DELETE              => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_DELETE,
                                                             self::CONVERT_TO_API             => true,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_VOID,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE             => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_POST,
                                                             self::CONVERT_TO_API             => true,
                                                             self::CONVERT_FROM_API_RULE      => self::CONVERT_RESULT_TO_API,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_WITH_FILE   => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_POST,
                                                             self::CONVERT_TO_API             => false,
                                                             self::CONVERT_FROM_API_RULE      => self::CONVERT_RESULT_TO_API,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_RAW         => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_POST,
                                                             self::CONVERT_TO_API             => false,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_RESULT_ONLY,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_GET_RAW     => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_GET,
                                                             self::CONVERT_TO_API             => false,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_ALL,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_POST_RAW    => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_POST,
                                                             self::CONVERT_TO_API             => false,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_RESPONSE_ONLY,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_POST_RAW_RAW_RETURN =>
                                                           [ self::METHOD                     => APIConstants::API_HTTP_METHOD_POST,
                                                             self::CONVERT_TO_API             => false,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_ALL,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_PATCH_RAW   => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_PATCH,
                                                             self::CONVERT_TO_API             => false,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_RESPONSE_ONLY,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_PATCH_RAW_RAW_RETURN =>
                                                            [ self::METHOD                     => APIConstants::API_HTTP_METHOD_PATCH,
                                                              self::CONVERT_TO_API             => false,
                                                              self::CONVERT_FROM_API_RULE      => self::RETURN_ALL,
                                                              self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_DELETE_RAW  => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_DELETE,
                                                             self::CONVERT_TO_API             => false,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_RESPONSE_ONLY,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_OPERATION_EXECUTE_DELETE_RAW_RAW_RETURN =>
                                                            [ self::METHOD                     => APIConstants::API_HTTP_METHOD_DELETE,
                                                              self::CONVERT_TO_API             => false,
                                                              self::CONVERT_FROM_API_RULE      => self::RETURN_ALL,
                                                              self::SKIP_CONVERT_ON_ERROR_CODE => 300 ],
        APIConstants::API_QUERY                         => [ self::METHOD                     => APIConstants::API_HTTP_METHOD_POST,
                                                             self::CONVERT_TO_API             => false,
                                                             self::CONVERT_FROM_API_RULE      => self::RETURN_ALL,
                                                             self::SKIP_CONVERT_ON_ERROR_CODE => 999 ],
        // do not replace on error
    ];

    /**
     * @param string     $url
     * @param string     $apiID
     * @param string     $version
     * @param string     $operation
     * @param array|null $payload
     *
     * @return array|null
     * @throws APIException
     */
    protected function invokeAPI(string $url, string $apiID, string $version, string $operation, ?array $payload, array $headers = []): ?array
    {
        $operationRules = self::API_OPERATION_MAP[$operation] ?? [];
        if (empty($operationRules)) {
            $error = "API client error: Key: $operation not found in array: self::API_OPERATION_MAP";
            logToFileError($error); // log error and throw Unsupported operation error.
            throw (new APIExecutionException())->setAPIError(APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0005,[],true));
        }
        $method = $operationRules[self::METHOD];

        $handler = RegistryLoader::getHandlerForVersion($apiID, $version);
        if ($handler === null) {
            throw ( new APIException() )->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0008, [
                    "RESOURCE" => $apiID,
                    "VERSION" => $version
                ], true));
        }

        $this->processRequestHeaders($headers, $operation);
        if ($payload != null && $operation == APIConstants::API_OPERATION_EXECUTE_GET_RAW) {
            // This condition will apply for GET operation if the payload is not null and is already in API format
            // The payload will be appended to the URL as URL parameters
            self::buildGETUrl($url, $payload);
            $apiPayload = $payload;
        } else if ($payload === null || ! $operationRules[self::CONVERT_TO_API]) {
            $apiPayload = $payload;
        } else if ( $operationRules[self::CONVERT_TO_API] ) {
            $apiPayload = APIUtil::convertEnt2ApiCollection(
                    ObjectAPIAdapter::CALLER_API,
                    $operation,
                    $apiID, $version, [ $payload ],
                    $handler->getRequestHandler(),
                    $handler->getAdapter()
                )[0];
        }

        $response = $this->call($url, $apiPayload, $method, $headers);
        if ( $response[self::HTTP_CODE] === 404 && empty($response[self::HTTP_RESPONSE]) ) {
            // special case of DS not reachable ?
            $result = [
                APIError::KEY_STATUS => $response[self::HTTP_CODE],
                APIError::KEY_DETAILS => $response[self::HTTP_RESPONSE]
            ];
            logToFileError("API client error: " . print_r($result, true));
            $error = APIError::getInstance(APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0004, [
                "OPERATION" => APIConstants::getOperation($operation),
                "VERSION" => $version,
                "HANDLER" => $apiID
            ]);
            throw (new APIExecutionException())->setAPIError($error);
            // FIXME: how do I add the error details from response & the http code in the exception?
        } else {
            // TODO: Objectify the logic based on $operation into APIOperation classes
            switch ($operationRules[self::CONVERT_FROM_API_RULE]) {
                case self::RETURN_ALL:
                    $result = $response;
                    break;
                case self::RETURN_RESPONSE_ONLY:
                    $result = $response[self::HTTP_RESPONSE];
                    break;
                case self::RETURN_RESULT_ONLY:
                    $result = $response[self::HTTP_RESPONSE][APIConstants::IA_RESULT_KEY];
                    break;

                case self::CONVERT_RESULT_TO_API:
                    if ($response[self::HTTP_CODE] < $operationRules[self::SKIP_CONVERT_ON_ERROR_CODE]) {
                        if ($response[self::HTTP_CODE] === self::HTTP_NO_CONTENT) {
                            $result = null;
                        } else {
                            $result = APIUtil::convertApi2EntCollection(ObjectAPIAdapter::CALLER_API,
                                //$operation,
                                APIConstants::API_OPERATION_OPTIONS,
                                $apiID, $version,
                                [$response[self::HTTP_RESPONSE][APIConstants::IA_RESULT_KEY]],
                                $handler->getResponseHandler(),
                                $handler->getAdapter())[0];
                        }
                    } else {
                        $result = $response[self::HTTP_RESPONSE][APIConstants::IA_RESULT_KEY];
                    }
                    break;

                case self::RETURN_VOID:
                    $result = null;
                    break;
                default:
                    $error = "API client error: Key: $operation has invalid convertFromAPIRule";
                    logToFileError($error); // log error and throw Unsupported operation error.
                    throw (new APIExecutionException())->setAPIError(APIError::getInstance(
                        APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0005,[],true));
            }

            $this->processResponseHeaders($response[self::HTTP_HEADERS]);
        }

        return $result;
    }

    /**
     * Add Content-Type headers and X-Trusted-Bearer + X-Trusted-Context tokens to headers
     *
     * @return array $headers
     */
    private function processRequestHeaders(&$headers, $operation) : array
    {
        if ($operation !== APIConstants::API_OPERATION_EXECUTE_WITH_FILE) {
            $headers[] = APIWebhook::DELIVER_CONTENT_TYPE;
        }

        $containAuthorizationBearer = APIUtil::getHeader($headers, "Authorization: Bearer");

        if (empty($containAuthorizationBearer)) {
            $headers[] = APIConstants::API_EXTRA_PARAM_HEADER_AUTHORIZATION . ":" . APIConstants::X_TRUSTED_BEARER . " " . (DomainServiceUtil::getServiceSecretKey() ?? '');

            $context = Globals::$g->serviceMeta->getContext();
            $context->gatherContext();
            $contextString = str_replace(array('+', '/'), array('-', '_'), base64_encode($context->toJson()));
            $headers[] = APIConstants::X_TRUSTED_CONTEXT . ":" . $contextString;
        }

        return $headers;
    }

    /**
     * @param array $headers
     */
    private function processResponseHeaders(array $headers)
    {
        if (null === ($trustedContext = ($headers[APIConstants::X_TRUSTED_CONTEXT] ?? ($headers[strtolower(APIConstants::X_TRUSTED_CONTEXT)] ?? null)))) {
            return;
        }
        $contextDecoded = base64_decode(
            str_replace(array('-', '_'), array('+', '/'), $trustedContext)
        );
        if ($contextDecoded !== false) {
            $contextData = json_decode($contextDecoded, JSON_OBJECT_AS_ARRAY);
            if ($contextData !== false) {
                $context = Globals::$g->serviceMeta->getContext();
                $context->initFromArray($contextData);
                $context->propagate();
            }
        }
    }


    /**
     * @param string     $url
     * @param array|null $payload
     * @param string     $method
     * @param array      $headers
     *
     * @return array
     */
    private function call(string $url, ?array $payload, string $method, array $headers): array
    {
        $doBind = false;
        if (in_array(APIWebhook::DELIVER_CONTENT_TYPE, $headers)) {
            if ($payload !== null) {
                $payload = json_encode($payload);
            }
            $doBind = true;
        }

        Util::httpCall($url, $payload, $restResonse,
            false, null, null, $doBind,
            $headers, $method, null, true,
            $retHeaders, true, $curlInfo, DomainServiceUtil::getTrustedApiTimeout());

        $httpCode = $curlInfo['http_code'];
        $errno = $curlInfo['curl_errno'];
        $errmsg = $curlInfo['curl_error'];
        if ($restResonse) {
            $result = json_decode($restResonse, true);
            if ($result === null) {
                $result = [
                    APIError::KEY_ERROR_MESSAGE => APIUtil::getJsonError(),
                    APIError::KEY_DETAILS => $restResonse];
                $httpCode = $httpCode <= 300 ? 400 : $httpCode;
            }
        }
        $result = $result ?? [];
        if ($errno && $httpCode <= 300) { // if not set $httpCode will be 0
            $result[APIError::KEY_DETAILS] = $errmsg;
            $httpCode = 500;
        }
        return [
            self::HTTP_RESPONSE => $result,
            self::HTTP_CODE => $httpCode,
            self::HTTP_HEADERS => $retHeaders,
        ];
    }

    /**
     * Build GET url parameters based on payload
     *
     * @param string $url
     * @param array  $payload
     *
     * @return void
     */
    public static function buildGETUrl(string &$url, array &$payload) {
        $flattened = $payload;
        array_walk($flattened, function(&$value, $key) {
            $value = "{$key}={$value}";
        });
        $url .= "?" . implode("&", $flattened);
        $payload = null;
    }

}
