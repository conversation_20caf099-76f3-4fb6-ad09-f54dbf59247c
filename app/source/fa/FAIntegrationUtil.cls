<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Sage Intacct Corporation, All Rights Reserved
 */
class FAIntegrationUtil extends DomainServiceIntegrationUtil
{
    const FA_DS_VERSION = '2i';
    const FA_QS_VERSION = '1-beta2';
    const FA_DS_API_URL_PREFIX = 'ia-ds-fa';
    const FA_DS_ASSET_OBJECT = 'fixed-assets/asset';
    const FA_DS_DOMAIN = 'fixed-assets';

    /**
     * @param string|array $scheduleEntryKeys A single schedule entry key or an array of keys.
     * @param string|null $actualPostingDate Optional posting date in 'YYYY-MM-DD' format.
     *
     * @return bool
     * @throws IAException
     */
    public static function postDeprSchEntry($scheduleEntryKeys, $actualPostingDate = null)
    {
        static $FA_DS_SERVICE = 'depreciation';
        static $FA_DS_FUNCTION = 'post-schedule';

        $ok = false;

        if (!is_array($scheduleEntryKeys)) {
            $scheduleEntryKeys = [$scheduleEntryKeys];
        }

        if (empty($scheduleEntryKeys)) {
            return true;
        }

        $dsAPIClient = new DomainServiceAPIFunctionServiceClient(
            self::FA_DS_API_URL_PREFIX,
            self::FA_DS_DOMAIN,
            $FA_DS_SERVICE,
            self::FA_DS_VERSION
        );

        $payload = [
            'scheduleEntryKeys' => $scheduleEntryKeys,
        ];

        // Add the actual posting date if provided
        if ($actualPostingDate) {
            $payload['actualPostingDate'] = $actualPostingDate;
        }

        try {
            $response = $dsAPIClient->invokeFunction($FA_DS_FUNCTION, $payload, APIConstants::API_OPERATION_EXECUTE_RAW);
            if (isset($response['ia::error'])) {
                self::handleAPIError($response['ia::error'], 'FA-0012');
            } elseif (isArrayValueProvided($response, 'data') || (isset($response['success']) && $response['success'])) {
                // Do something with $response['data'] ?
                $ok = true;
            } else {
                self::addUnexpectedResponseError($response);
            }
        } catch (Exception $e) {
            Globals::$g->gErr->addError('FA-0012', GetFL(), $e->getMessage());
        }
        return $ok;
    }

    /**
     * @param array $scheduleEntryKeys An array of keys.
     *
     * @return bool
     * @throws IAException
     */
    public static function unpostDeprSchEntry($scheduleEntryKeys)
    {
        static $FA_DS_SERVICE = 'depreciation';
        static $FA_DS_FUNCTION = 'unpost-schedule';

        $ok = false;

        if (!is_array($scheduleEntryKeys)) {
            $scheduleEntryKeys = [$scheduleEntryKeys];
        }

        if (empty($scheduleEntryKeys)) {
            return true;
        }

        $dsAPIClient = new DomainServiceAPIFunctionServiceClient(
            self::FA_DS_API_URL_PREFIX,
            self::FA_DS_DOMAIN,
            $FA_DS_SERVICE,
            self::FA_DS_VERSION
        );

        $payload = [
            'scheduleEntryKeys' => $scheduleEntryKeys,
        ];

        try {
            $response = $dsAPIClient->invokeFunction($FA_DS_FUNCTION, $payload, APIConstants::API_OPERATION_EXECUTE_RAW);
            if (isset($response['ia::error'])) {
                self::handleAPIError($response['ia::error'], 'FA-0012');
            } elseif (isArrayValueProvided($response, 'data') || (isset($response['success']) && $response['success'])) {
                // Do something with $response['data'] ?
                $ok = true;
            } else {
                self::addUnexpectedResponseError($response);
            }
        } catch (Exception $e) {
            Globals::$g->gErr->addError('FA-0012', GetFL(), $e->getMessage());
        }
        return $ok;
    }

    /**
     * @param array $payload
     * @param array $results
     *
     * @return bool
     * @throws IAException
     */
    public static function queryFixedAssetService($payload, &$results)
    {
        $dsAPIClient = new \DomainServiceAPICoreServiceClient(self::FA_DS_API_URL_PREFIX, 'core/'.APIConstants::API_QUERY, self::FA_QS_VERSION);
        $ok = false;
        $results = null;

        try {
            $response = $dsAPIClient->invokeService($payload, APIConstants::API_QUERY);
            if (isset($response['httpCode'])) {
                $httpCode = $response['httpCode'];
                if ($httpCode == 200) {
                    if (isset($response['response']['ia::result'])) {
                        $results = $response['response']['ia::result'];
                        if (isset($results['ia::error'])) {
                            self::handleAPIError($response['ia::error'],'FA-0010');
                        } else {
                            $ok = true;
                        }
                    } else {
                        Globals::$g->gErr->addError('FA-0010', GetFL(), 'Invalid API response (no ia::result)');
                    }
                } else {
                    if (isset($response['response']) && is_string($response['response'])) {
                        $errorResponse = ' - ' . $response['response'];
                    } else {
                        $errorResponse = '';
                    }
                    Globals::$g->gErr->addError('FA-0010', GetFL(), "Error in API call with HTTP code: $httpCode $errorResponse");
                }
            }
        } catch (Exception $e) {
            Globals::$g->gErr->addError('FA-0010', GetFL(), $e->getMessage());
        }
        return $ok;
    }

    /**
     * @param bool|null $isAPEnabledInFA
     *
     * @return bool
     * @throws IAException
     */
    public static function isAPEnabledInFixedAsset(&$isAPEnabledInFA)
    {
        $ok = true;
        static $cachedAPEnabledInFA = null;

        if ( ! isset($cachedAPEnabledInFA)) {
            $payload = [
                'object' => 'fixed-assets/setup',
                'fields' => ['enableAccountsPayable'],
            ];
            $ok = self::queryFixedAssetService($payload, $results);
            if ($ok) {
                if (empty($results)) {
                    $cachedAPEnabledInFA = false;
                } elseif (isset($results[0]['enableAccountsPayable'])) {
                    $cachedAPEnabledInFA = ($results[0]['enableAccountsPayable'] == 'true');
                } else {
                    Globals::$g->gErr->addError('', GetFL(), 'Invalid API response to query enableAccountsPayable, results: ' . var_export($results, true));
                    $ok = false;
                }
            }
        }
        $isAPEnabledInFA = $cachedAPEnabledInFA;

        return $ok;
    }

    /**
     * @param bool|null $isPOEnabledInFA
     *
     * @return bool
     * @throws IAException
     */
    public static function isPOEnabledInFixedAsset(&$isPOEnabledInFA)
    {
        $ok = true;
        static $cachedPOEnabledInFA = null;

        if ( ! isset($cachedAPEnabledInFA)) {
            $payload = [
                'object' => 'fixed-assets/setup',
                'fields' => ['enablePurchasingTxns'],
            ];
            $ok = self::queryFixedAssetService($payload, $results);
            if ($ok) {
                if (empty($results)) {
                    $cachedPOEnabledInFA = false;
                } elseif (isset($results[0]['enablePurchasingTxns'])) {
                    $cachedPOEnabledInFA = ($results[0]['enablePurchasingTxns'] == 'true');
                } else {
                    Globals::$g->gErr->addError('', GetFL(), 'Invalid API response to query enablePurchasingTxns, results: ' . var_export($results, true));
                    $ok = false;
                }
            }
        }
        $isPOEnabledInFA = $cachedPOEnabledInFA;

        return $ok;
    }

    /**
     * @param array $classifications
     * @param array $internalGLAcctFilter
     *
     * @return bool
     * @throws IAException
     */
    public static function queryClassificationsWithAssetGLAccounts(&$classifications, $internalGLAcctFilter = null)
    {
        $ok = true;
        static $cachedClassifications = null;

        if ( ! isset($cachedClassifications)) {
            $glAcctFilter = $internalGLAcctFilter ? ['$eq' => $internalGLAcctFilter] : ['$ne' => ['assetGLAccount.key' => null]];
            $payload = [
                'object'  => 'fixed-assets/asset-classification',
                'fields'  => ['key', 'id', 'name', 'assetGLAccount.key', 'assetGLAccount.id', 'assetGLAccount.name'],
                'filters' => [
                    ['$eq' => ['status' => 'active']],
                    $glAcctFilter
                ],
                'filterExpression' => 'and',
                'size' => 1000,
            ];
            $ok = self::queryFixedAssetService($payload, $results);
            if ($ok) {
                $cachedClassifications = [];
                foreach ($results as $result) {
                    $cachedClassifications[$result['assetGLAccount.id']] = $result['key'];
                }
            }
        }
        $classifications = $cachedClassifications;

        return $ok;
    }


    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    public static function initExistingAssetsInfo(&$values)
    {
        $payload = [
            'object'  => self::FA_DS_ASSET_OBJECT,
            'fields'  => ['key', 'id', 'name', 'source.headerKey', 'source.headerId', 'source.lineKey', 'source.lineNumber'],
            'filters' => [
                ['$eq' => ['source.headerKey' => $values['RECORDNO']]],
            ],
            'filterExpression' => 'and',
        ];
        $ok = self::queryFixedAssetService($payload, $results);
        if ($ok) {
            $resultByLineKey = [];
            foreach ($results as $result) {
                $resultByLineKey[$result['source.lineKey']] = $result;
            }
            $values[':existingAssetsInfo'] = $resultByLineKey;
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    public static function createAssets(&$values)
    {
        $assetLines = [];
        $ok = true;
        $dsAPIClient = new \DomainServiceAPIObjectClient(self::FA_DS_API_URL_PREFIX, self::FA_DS_ASSET_OBJECT, self::FA_DS_VERSION);
        FAIntegrationUtil::queryClassificationsWithAssetGLAccounts($classifications);

        foreach ($values['ITEMS'] as &$item) {
            if (isArrayValueProvided($item, 'SUBTOTAL') || (($item['AMOUNT'] < 0)  && ($values['MODULEKEY'] === "9.PO"))) {
                // only process the primary lines, when subtotal is set to null. Also ignore negative amount.
                continue;
            }

            // Skip items that already have assets created from previous operations
            if (isArrayValueProvided($item, 'ASSETDIMKEY') && 
                isArrayValueProvided($item, 'RECORDNO') && 
                isset($values[':existingAssetsInfo'][$item['RECORDNO']])) {
                continue;
            }

            if ( isset($item['ALLOCATIONKEY']) && isset($assetLines[$item['LINE_NO']]) ) {
                // when using allocation, we re-use the same asset across lines.
                $asset = $assetLines[$item['LINE_NO']];
                $item['ASSETDIMKEY'] = $asset['key'];
                $item['ASSETID'] = $asset['id'];
                self::setChildEntriesAssetDimension($item, $values['ITEMS']);
            } else {
                $glAccountId = explode('--', $item['ACCOUNTNO'])[0];
                // For tax items, we do not create asset as it's already created from parent record and hence acquired asset name should not be provided
                if (isset($classifications[$glAccountId]) && !isArrayValueTrue($item, 'ISTAX')) {
                    if (isArrayValueProvided($item, 'RECORDNO') && isset($values[':existingAssetsInfo'][$item['RECORDNO']])) {
                        // If asset associated with record already exists, skip creating
                        continue;
                    }

                    // VALIDATION: Check for conflicting asset dimension and NAMEOFACQUIREDASSET first before clearing to avoid errors
                    // on existing line items during deletion and addition of a line item scenario
                    if (isArrayValueProvided($item, 'NAMEOFACQUIREDASSET') &&
                        (isArrayValueProvided($item, 'ASSETID') || isArrayValueProvided($item, 'ASSETDIMKEY'))) {
                        $lineNumber = $item['LINE_NO'];
                        $msg = "Line $lineNumber cannot have an asset dimension because \"Name of acquired asset\" is specified.";
                        Globals::$g->gErr->addIAFieldError('ASSETID', 'ITEMS/APBILLITEM',
                                                           'FA-0007', GetFL(), $msg, ['LINE_NUMBER' => $lineNumber], index:$lineNumber);
                        $ok = false;
                        break;
                    }

                    // NOW clear ASSETDIMKEY as needed to make sure deletion and addition of a line item scenario works as expected
                    if (isArrayValueProvided($item, 'ASSETDIMKEY') &&
                        isArrayValueProvided($item, 'NAMEOFACQUIREDASSET') &&
                        isArrayValueProvided($item, 'RECORDNO') &&
                        !isset($values[':existingAssetsInfo'][$item['RECORDNO']])) {
                        // Clear the asset dimension since the asset was deleted
                        unset($item['ASSETDIMKEY']);
                        unset($item['ASSETID']);
                    }

                    if (!isArrayValueProvided($item, 'NAMEOFACQUIREDASSET')) {
                        $lineNumber = $item['LINE_NO'];
                        $msg = "Enter a \"Name of acquired asset\" for line $lineNumber and account $glAccountId.";
                        Globals::$g->gErr->addIAFieldError('NAMEOFACQUIREDASSET', 'ITEMS/APBILLITEM',
                                                           'FA-0005', GetFL(), $msg, ['LINE_NUMBER' => $lineNumber, 'GL_ACCOUNT' => $glAccountId], index:$lineNumber);
                        $ok = false;
                        break;
                    }
                    if (!isArrayValueProvided($item, 'AMOUNT')) {
                        $lineNumber = $item['LINE_NO'];
                        $msg = "Enter an amount on line $lineNumber.";
                        Globals::$g->gErr->addIAError('FA-0006', GetFL(), $msg, ['LINE_NUMBER' => $lineNumber]);
                        $ok = false;
                        break;
                    }
                    if (isArrayValueProvided($item, 'ALLOCATIONKEY', 0)) {
                        $msg = "You cannot acquire asset and use custom allocations in the same line item";
                        Globals::$g->gErr->addIAError('FA-0002', GetFL(), $msg, ['LINE_NUMBER' => $item['LINE_NO']]);
                        $ok = false;
                        break;
                    }

                    $classificationKey = $classifications[$glAccountId];
                    $reclaimableTaxAmount = '0.00';
                    if (isset($item['RECORDNO'])) {
                        $reclaimableTaxAmount = self::getReclaimableTaxForBaseItem($values['ITEMS'], $item['RECORDNO']);
                    }
                    $assetCost = self::computeAssetCost($item, $values, $reclaimableTaxAmount);

                    $record = [
                        'name'              => $item['NAMEOFACQUIREDASSET'],
                        'description'       => $item['ENTRYDESCRIPTION'],
                        'acquisitionDate'   => date("Y-m-d", strtotime($item['ENTRY_DATE'])), // '2021-10-29',
                        'assetCost'         => $assetCost,
                        'classificationKey' => $classificationKey,
                        'locationDimKey'    => $item['LOCATION#'],
                        'quantity'          => 1,
                        'depreciate'        => true,
                        'state'             => 'readyForReview',
                        'status'            => 'active',
                        'assetType'         => 'tangible',
                        'salvageValue'      => '0',
                        'sourceHeaderType'  => 'IA.BILL',
                        'sourceHeaderKey'   => $values['RECORDNO'],
                        'sourceHeaderId'    => $values['RECORDID'],
                        'sourceLineKey'     => $item['RECORDNO'],
                        'sourceLineNo'      => $item['LINE_NO'],
                    ];

                    if (!empty($item['ALLOCATIONKEY'])) {
                        $record['allocationKey'] = $item['ALLOCATIONKEY'];
                    }

                    self::initializeRecordDimensions($record, $item);
                    self::initializeRecordCustomComponents($record, $item);

                    try {
                        $response = $dsAPIClient->create($record);
                        if (isset($response['ia::error'])) {
                            self::handleAPIError($response['ia::error'], 'FA-0012');
                            $ok = false;
                        } elseif (isArrayValueProvided($response, 'key') && isArrayValueProvided($response, 'id')) {
                            $item['ASSETDIMKEY'] = $response['key'];
                            $item['ASSETID'] = $response['id'];
                            self::setChildEntriesAssetDimension($item, $values['ITEMS']);
                            $assetLines[$item['LINE_NO']] = $response;
                        } else {
                            self::addUnexpectedResponseError($response);
                            $ok = false;
                        }
                    } catch (Exception $e) {
                        Globals::$g->gErr->addError('FA-0012', GetFL(), $e->getMessage());
                        $ok = false;
                    }
                } else {
                    if (isArrayValueProvided($item, 'NAMEOFACQUIREDASSET')) {
                        $lineNumber = $item['LINE_NO'];
                        $msg = "Line $lineNumber cannot have a \"Name of acquired asset\" because the account $glAccountId is not associated with an asset classification.";
                        Globals::$g->gErr->addIAFieldError('NAMEOFACQUIREDASSET', 'ITEMS/APBILLITEM', 'FA-0004',
                                                           GetFL(), $msg, ['LINE_NUMBER' => $lineNumber, 'GL_ACCOUNT' => $glAccountId], index:$lineNumber);
                        $ok = false;
                    }
                }
                if (!$ok) {
                    break;
                }
            }
        }
        return $ok;
    }

    /**
     * @param array $items       The list of items to process.
     * @param mixed $baseRecordNo The base record number to match against.
     *
     * @return string The total reclaimable tax amount.
     */
    private static function getReclaimableTaxForBaseItem(array $items, $baseRecordNo): string
    {
        $amount = '0.00';

        foreach ($items as $lineItem) {
            if (
                ($lineItem['PARENTENTRY'] ?? null) == $baseRecordNo &&
                isArrayValueTrue($lineItem, 'ISTAX') &&
                isArrayValueFalse($lineItem, 'ISOFFSET') &&
                isArrayValueTrue($lineItem, 'ISRECLAIMABLE')
            ) {
                $amount = ibcadd($amount, $lineItem['AMOUNT']);
            }
        }

        return $amount;
    }

    /**
     * @param array $item
     * @param array $values
     * @param string $reclaimableTaxAmount The total reclaimable tax amount to exclude from asset cost if applicable.
     * @return string Computed asset cost.
     */
    private static function computeAssetCost($item, $values, string $reclaimableTaxAmount)
    {
        // New condition to handle Purchasing + Allocation case specially
        if (
            isset($values['MODULEKEY']) && $values['MODULEKEY'] === "9.PO" &&
            !empty($item['ITEMID'])
        ) {
            $assetCost = self::computeAssetCostForPurchasingWithAllocation($item, $values);
            if ($assetCost === null || $assetCost === '0.0') {
                throw new IAException('Asset cost could not be determined from allocation splits.');
            }
            // Return early for PO flow - tax calculations are handled in computeAssetCostForPurchasingWithAllocation
            return $assetCost;
        }
        // start with the baseline value or original amount when using allocation
        // note: we should not use taxes when using allocation
        $assetCost = !empty($item['ALLOCATIONKEY']) ? $item['ORIGAMOUNT'] : $item['AMOUNT'];
        if ($item['INCLUDETAXINASSETCOST'] === 'true') {
            if (isset($values['TAXSOLUTIONID']) && isset($item['DOC_LINENO'])) {
                foreach ($values['ITEMS'] as $lineItem) {
                    // only include the values from the current taxes lines
                    if ($item['DOC_LINENO'] === $lineItem['DOC_LINENO'] && $lineItem['ISTAX'] === 'true') {
                        $assetCost = ibcadd($assetCost, $lineItem["AMOUNT"]);
                    }
                }
            } else {
                if (isset($item['BASETAXAMOUNT'])) {
                    $assetCost = ibcadd($assetCost, $item['BASETAXAMOUNT']);
                }
            }

            // Subtract total reclaimable tax (Base tax - recoverable for ITC Canada scenario) amount from asset cost
            if ( $reclaimableTaxAmount > 0 ) {
                $assetCost = ibcsub($assetCost, $reclaimableTaxAmount);
            }
        }
        return $assetCost ?: '0.0'; // return 0 instead of null
    }

    /**
     * Computes the asset cost for a Purchasing with Allocation scenario.
     *
     * Iterates over all line items to find matching splits (non-offset)
     * based on ALLOCATIONKEY and LINE_NO, and sums their AMOUNT.
     * Also handles tax inclusion and reclaimable tax deduction for PO flow.
     *
     * @param array $item   The individual line item being processed
     * @param array $values The complete payload of values which include all line items
     *
     * @return string The computed asset cost in string decimal format. Returns '0.0' if no matching splits found.
     */
    private static function computeAssetCostForPurchasingWithAllocation($item, $values)
    {
        $allocationKey = (string) ($item['ALLOCATIONKEY'] ?? '');
        $lineNo        = (string) ($item['LINE_NO'] ?? '');

        $total = '0.0';

        if ($lineNo && !empty($values['ITEMS'])) {
            foreach ($values['ITEMS'] as $split) {
                if (
                    empty($split['ISOFFSET']) &&
                    (string)($split['ALLOCATIONKEY'] ?? '') === $allocationKey &&
                    (string)($split['LINE_NO'] ?? '') === $lineNo
                ) {
                    $amount = $split['AMOUNT'] ?? '0.0';
                    $total = ibcadd($total, $amount);
                }
            }
        }

        // Handle tax inclusion for PO flow
        if ($item['INCLUDETAXINASSETCOST'] === 'true') {
            $taxAmount = '0.0';
            $reclaimableTaxAmount = '0.0';

            // Find tax lines with matching DOC_LINENO
            foreach ($values['ITEMS'] as $lineItem) {
                if (isArrayValueTrue($lineItem, 'ISTAX') &&
                    !isArrayValueTrue($lineItem, 'ISOFFSET') &&
                    isset($item['DOC_LINENO']) &&
                    $item['DOC_LINENO'] === $lineItem['DOC_LINENO']) {

                    $taxAmount = ibcadd($taxAmount, $lineItem['AMOUNT']);

                    // Track reclaimable tax separately
                    if (isArrayValueTrue($lineItem, 'ISRECLAIMABLE')) {
                        $reclaimableTaxAmount = ibcadd($reclaimableTaxAmount, $lineItem['AMOUNT']);
                    }
                }
            }

            // Add all taxes to asset cost
            $total = ibcadd($total, $taxAmount);

            // Subtract reclaimable tax amount
            if ($reclaimableTaxAmount > 0) {
                $total = ibcsub($total, $reclaimableTaxAmount);
            }
        }

        return $total;
    }

    /**
     * @param array  $iaError
     * @param string $number
     *
     * @return void
     * @throws IAException
     */
    protected static function handleAPIError($iaError, $number)
    {
        $errorAdded = false;
        if (isset($iaError['details']) && is_array($iaError['details'])) {
            foreach ($iaError['details'] as $errorDetail) {
                if (self::addErrorFromMessageId($errorDetail)) {
                    $errorAdded = true;
                }
            }
        }
        if (!$errorAdded) {
            $errorAdded = self::addErrorFromMessageId($errorDetail);
        }
        if (!$errorAdded) {
            if (isset($iaError['message'])) {
                Globals::$g->gErr->addError($number, GetFL(), $iaError['message']);
                $errorAdded = true;
            }
        }
        if (!$errorAdded) {
            self::addUnexpectedResponseError($iaError);
        }
    }

    protected static function addErrorFromMessageId(?array $iaError) : bool
    {
        $errorAdded = false;
        if (isset($iaError['errorId']) && !str_starts_with("IA.", $iaError['message'])) {
            Globals::$g->gErr->addError($iaError['errorId'], GetFL(), $iaError['message']);
            $errorAdded = true;
        } elseif (isset($iaError['additionalInfo']['messageId'])) {
            $text = $iaError['message'];
            if (str_starts_with("IA.", $iaError['message'])) {
                $text = I18N::getSingleToken($iaError['additionalInfo']['messageId'], $iaError['additionalInfo']['placeholders']);
            }
            if (isArrayValueProvided($iaError, 'target')) {
                $text .= ' (Target: ' . $iaError['target'] . ')';
            }
            Globals::$g->gErr->addError('', GetFL(), $text);
            $errorAdded = true;
        } elseif (isArrayValueProvided($iaError, 'code', 'PT00000110') && isset($iaError['message'])) {
            // This is error from Validation Trigger.  It doesn't needt to be translated since the user created it.
            Globals::$g->gErr->addError('', GetFL(), $iaError['message']);
            $errorAdded = true;
        }
        return $errorAdded;
    }

    /**
     * @param array $record
     * @param array $item
     *
     * @return void
     */
    private static function initializeRecordDimensions (&$record, &$item)
    {
        $managerFactory = Globals::$g->gManagerFactory;
        $dims = IADimensions::getAllDimensionObjectProperties();
        $allocationDimensions = [];
        if (isArrayValueProvided($item, 'ALLOCATIONKEY')) {
            // Cache the list of dimensions we don't want to clone from the bill to the asset.
            $allocEntries = AllocationManager::getAllocationLines($item['ALLOCATIONKEY']);
            if (is_array($allocEntries) && sizeof($allocEntries) > 1) {
                // find any dimension with value other than null
                foreach ($dims as $dim) {
                    $currentDimName = $dim['dimdbkey'] === 'DEPTKEY' ? 'DEPARTMENTKEY' : $dim['dimdbkey'];
                    foreach ($allocEntries as $allocEntry) {
                        if (isset($allocEntry[$currentDimName])) {
                            // distinct dimension to exclude.
                            $allocationDimensions[] = $dim['entity'];
                            break;
                        }
                    }
                }
            }
        }
        foreach ($dims as $dim) {
            $idName = $dim['path'];
            $keyName = $dim['dimdbkey'];
            $entity = $dim['entity'];
            $apiKeyName = $entity . 'DimKey';
            if (isArrayValueProvided($item, $keyName) || isArrayValueProvided($item, $idName)) {
                if (in_array($entity, $allocationDimensions)) {
                    // skip allocation dimensions
                    continue;
                }
                if (!isArrayValueProvided($item, $keyName)) {
                    $managerFactory->getManager($entity)->getRecordNoAndLocationFromVid($item[$idName], $item[$keyName], $loc);
                }
                $record[$apiKeyName] = $item[$keyName];
            }
        }
    }

    /**
     * @param array $record
     * @param array $item
     *
     * @return void
     */
    private static function initializeRecordCustomComponents(array &$record, array $item)
    {
        $manager = Globals::$g->gManagerFactory->getManager('fixedasset', true);
        $dimFieldNames = $manager->GetCustomDimFields();
        $customFieldsAsset = $manager->GetCustomFields();

        // Populate fixedasset record from apbillitem if both contains same custom field ID
        $managerAPBillItem = Globals::$g->gManagerFactory->getManager('apbillitem');
        $customFieldsAPBillItem = $managerAPBillItem->GetCustomFields();
        $customFieldsAPBillItemIDs = [];
        foreach ($customFieldsAPBillItem as $customFieldBillItem) {
            if (!$customFieldBillItem instanceof CustomFieldUddlookup) {
                $customFieldsAPBillItemIDs[] = $customFieldBillItem->getFieldName();
            }
        }

        foreach ($customFieldsAsset as $customFieldAsset) {
            if (in_array($customFieldAsset->getFieldName(), $customFieldsAPBillItemIDs)) {
                $record[$customFieldAsset->getFieldName()] = $item[$customFieldAsset->getFieldName()];
            }
        }

        $allocationDimensions = [];
        if (isArrayValueProvided($item, 'ALLOCATIONKEY')) {
            // Cache the list of dimensions we don't want to clone from the bill to the asset.
            $allocEntries = AllocationManager::getAllocationLines($item['ALLOCATIONKEY']);
            if ( is_array($allocEntries) && sizeof($allocEntries) > 1 ) {
                // find any custom dimension with value set
                foreach ($dimFieldNames as $customDim) {
                    $currentDimName = $customDim['path'];
                    foreach ($allocEntries as $allocEntry) {
                        if (isset($allocEntry[$currentDimName])) {
                            // custom dimension to exclude.
                            $allocationDimensions[] = $currentDimName;
                            break;
                        }
                    }
                }
            }
        }
        foreach ($dimFieldNames as $customDim) {
            $path = $customDim['path'];
            if (isArrayValueProvided($item, $path) && !in_array($path, $allocationDimensions)) {
                $record[$path] = $item[$path];
            }
        }
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    public static function updateAssetsSourceInfo(&$values)
    {
        $ok = true;
        $dsAPIClient = new \DomainServiceAPIObjectClient(self::FA_DS_API_URL_PREFIX, self::FA_DS_ASSET_OBJECT, self::FA_DS_VERSION);

        foreach ($values['ITEMS'] as &$item) {
            if (isArrayValueProvided($item, 'NAMEOFACQUIREDASSET')) {
                if (isset($values[':existingAssetsInfo'][$item['RECORDNO']])) {
                    $assetInfo = $values[':existingAssetsInfo'][$item['RECORDNO']];
                    if ( $assetInfo['source.headerId'] == $values['RECORDID']
                         && $assetInfo['source.lineNumber'] == $item['LINE_NO'] ) {
                        // Nothing to update, already match existing Asset info, skipping.
                        continue;
                    }
                    $record = [
                        'sourceHeaderType' => 'IA.BILL',
                        'sourceHeaderKey'  => $values['RECORDNO'],
                        'sourceHeaderId'   => $values['RECORDID'],
                        'sourceLineKey'    => $item['RECORDNO'],
                        'sourceLineNo'     => $item['LINE_NO'],
                    ];

                    try {
                        $response = $dsAPIClient->update($item['ASSETDIMKEY'], $record);
                        if ( isset($response['ia::error']) ) {
                            self::handleAPIError($response['ia::error'], 'FA-0011');
                            $ok = false;
                        }
                    } catch ( Exception $e ) {
                        Globals::$g->gErr->addError('FA-0011', GetFL(), $e->getMessage());
                        $ok = false;
                    }
                }
            }
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    public static function updateDeleteAssets(&$values)
    {
        $ok = true;
        $ok = $ok && self::updateAssetsSourceInfo($values);
        $ok = $ok && self::deleteAssetFromLineItem($values);
        return $ok;
    }

    /**
     * @param array $response
     *
     * @return void
     * @throws IAException
     */
    protected static function addUnexpectedResponseError(array $response) : void
    {
        logToFileInfo("Unexpected response: " . var_export($response, true));
        Globals::$g->gErr->addError('', GetFL(), 'Internal error -- Unexpected response: ' . var_export($response, true));
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    public static function deleteAssets(&$values)
    {
        $ok = true;
        $path = self::FA_DS_VERSION . '/objects/' . self::FA_DS_ASSET_OBJECT;

        foreach ( $values['ITEMS'] as &$item ) {
            if ( isArrayValueProvided($item, 'NAMEOFACQUIREDASSET') && isArrayValueProvided($item, 'ASSETDIMKEY') ) {
                try {
                    $response = RESTApiClient::execute($path, APIConstants::API_OPERATION_DELETE, null, [], $item['ASSETDIMKEY']);
                    if ( isset($response[APIConstants::IA_RESULT_KEY]['ia::error']) ) {
                        self::handleAPIError($response[APIConstants::IA_RESULT_KEY]['ia::error'], 'FA-0013');
                        $ok = false;
                        break;
                    }
                } catch ( Exception $e ) {
                    Globals::$g->gErr->addError('FA-0013', GetFL(), $e->getMessage());
                    $ok = false;
                    break;
                }
            }
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    public static function deleteAssetFromLineItem(&$values)
    {
        $ok = true;
        $path = self::FA_DS_VERSION . '/objects/' . self::FA_DS_ASSET_OBJECT;

        $existingAssetsInfos = $values[':existingAssetsInfo'];
        foreach ( $existingAssetsInfos as $key => $existingAssetsInfo ) {
            $found = false;
            foreach ( $values['ITEMS'] as &$item ) {
                if ( $item['RECORDNO'] == $key ) {
                    $found = true;
                    break;
                }
            }
            if ( ! $found ) {
                try {
                    $response = RESTApiClient::execute($path, APIConstants::API_OPERATION_DELETE, null, [], $existingAssetsInfo['key']);
                    if ( isset($response[APIConstants::IA_RESULT_KEY]['ia::error']) ) {
                        self::handleAPIError($response[APIConstants::IA_RESULT_KEY]['ia::error'], 'FA-0013');
                        $ok = false;
                        break;
                    }
                } catch ( Exception $e ) {
                    Globals::$g->gErr->addError('FA-0013', GetFL(), $e->getMessage());
                    $ok = false;
                }
            }
        }
        return $ok;
    }

    private static function setChildEntriesAssetDimension(array $parentItem, array &$items)
    {
        foreach ($items as &$item) {
            // APBill Line's children record such as the "hidden" tax lines
            $apBillLineChild = isArrayValueProvided($item, 'PARENTENTRY', $parentItem['RECORD#']);
            // APBill Line record that came from Tax Subtotal line from PO
            $apBillLineFromSubtotalTax = isArrayValueProvided($parentItem, 'DOC_LINENO', $item['DOC_LINENO']) && $item['ISTAX'] === 'true';
            if ($apBillLineChild || $apBillLineFromSubtotalTax) {
                $item['ASSETDIMKEY'] = $parentItem['ASSETDIMKEY'];
                $item['ASSETID'] = $parentItem['ASSETID'];
            }
        }
    }
    /**
     * Common function to prepare SFAM (Fixed Asset) fields visibility based on asset acquisition settings
     *
     * @param View $view The view object
     * @param array $obj The transaction data object
     * @param string $entriesKey The key for entries array ('ITEMS' or 'ENTRIES')
     * @param bool $isFixedAssetSupported Whether fixed asset is supported for this transaction type
     * @return void
     */
    public static function prepareSFAMFields($view, $obj, $entriesKey, $isFixedAssetSupported): void
    {
        if (!$isFixedAssetSupported || !isset($obj) || !isset($obj[$entriesKey]) || !is_array($obj[$entriesKey])) {
            return;
        }

        $isVatEnabled = TaxSetupManager::isVATEnabled();

        foreach ($obj[$entriesKey] as $entry) {
            if ($entry['SELECTEDASSETMODE'] && $isFixedAssetSupported) {
                // Determine asset mode flags for easier readability
                $isNoAsset = $entry['SELECTEDASSETMODE'] === 'N';        // No asset acquisition
                $isMultipleAsset = $entry['SELECTEDASSETMODE'] === 'M';  // Multiple asset acquisition

                // Update visibility properties
                $view->findAndSetProperty(array('path' => 'ASSETQUANTITY'), array('hidden' => !$isMultipleAsset));
                $view->findAndSetProperty(array('path' => 'NAMEOFACQUIREDASSET'), array('hidden' => $isNoAsset));
                if ($isVatEnabled) {
                    $view->findAndSetProperty(array('path' => 'INCLUDETAXINASSETCOST'), array('hidden' => $isNoAsset));
                }
            }
        }
    }
}