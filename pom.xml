<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.intacct.parent</groupId>
        <artifactId>ia-parent-pom</artifactId>
        <version>1.0.10</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.intacct</groupId>
    <artifactId>ia-ds-fa-pom</artifactId>
    <version>8.1.0-26-SNAPSHOT</version>
    <name>ia-ds-fa-pom</name>
    <description>Fixed Asset Domain Service POM project</description>
    <packaging>pom</packaging>

    <scm>
        <connection>scm:git:${project.scm.url}</connection>
        <developerConnection>scm:git:${project.scm.url}</developerConnection>
        <url>https://github.com/intacct/ia-ds-fa.git</url>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <ia.core.fmwk.version>9.11.0-5</ia.core.fmwk.version>
        <ia.ds.php.client.version>7.13.0-1</ia.ds.php.client.version>
        <clientgenerator.version>5.1.0-2</clientgenerator.version>
        <registry.version>4.2.0-1</registry.version>

        <spring-boot-maven-plugin.version>3.5.5</spring-boot-maven-plugin.version>
        <maven.compiler.version>3.14.0</maven.compiler.version>
        <maven-surefire.version>3.5.2</maven-surefire.version>
        <maven-release-plugin.version>3.1.1</maven-release-plugin.version>
        <maven-antrun.version>3.1.0</maven-antrun.version>
        <jacoco.version>0.8.13</jacoco.version>
        <lombok.version>1.18.40</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- ia-core -->
            <dependency>
                <groupId>com.intacct.core</groupId>
                <artifactId>ia-core-fmwk</artifactId>
                <version>${ia.core.fmwk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.intacct.core</groupId>
                <artifactId>ia-core-deploy</artifactId>
                <version>${ia.core.fmwk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.intacct.core</groupId>
                <artifactId>ia-core-fmwk-test</artifactId>
                <version>${ia.core.fmwk.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.intacct</groupId>
                <artifactId>ia-ds-php-client</artifactId>
                <version>${ia.ds.php.client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.6.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.8.1</version>
                <executions>
                    <execution>
                        <id>resource-dependencies</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.intacct.core</groupId>
                                    <artifactId>ia-core-fmwk</artifactId>
                                    <version>${ia.core.fmwk.version}</version>
                                    <outputDirectory>${project.build.directory}/classes</outputDirectory>
                                    <includes>kafka/**</includes>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>service</module>
        <module>client</module>
    </modules>

</project>
