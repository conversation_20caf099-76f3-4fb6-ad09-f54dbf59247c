name: "Copy image from GHCR "

on: 
  workflow_dispatch:
    inputs:
      source-repository:
        required: true
        type: choice
        options:
          - ia-ds-docker-dev
          - ia-ds-docker-prod
        default: "ia-ds-docker-dev"
        description: "Source repository namespace in GitHub Container Registry"
      source-image:
        required: true
        type: string
        default: ""
        description: "Source GHCR image name with version (e.g., image:version)"
      target-repository:
        required: true
        type: choice
        options:
          - ia-ds-docker-dev
          - ia-ds-docker-prod
        default: "ia-ds-docker-dev"
        description: "Target repository namespace in GitHub Container Registry"

permissions:
  id-token: write
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  copy_image:
    name: Pull and Push Docker Image
    runs-on: ubuntu-latest
    steps:
    - name: Log in to GHCR
      uses: docker/login-action@v3.0.0
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Pull, Tag, and Push Docker Image
      id: pull_push
      run: |
        set -e
        SOURCE_REPO=${{ inputs.source-repository }}
        SOURCE_IMAGE=${{ inputs.source-image }}
        TARGET_REPO=${{ inputs.target-repository }}

        IMAGE_NAME=$(echo $SOURCE_IMAGE | cut -d: -f1)
        IMAGE_VERSION=$(echo $SOURCE_IMAGE | cut -d: -f2)

        echo "Pulling the image from GHCR: $REGISTRY/intacct/$SOURCE_REPO/$SOURCE_IMAGE"
        docker pull $REGISTRY/intacct/$SOURCE_REPO/$SOURCE_IMAGE

        echo "Tagging the image for target GHCR repository"
        docker tag $REGISTRY/intacct/$SOURCE_REPO/$SOURCE_IMAGE $REGISTRY/intacct/$TARGET_REPO/$IMAGE_NAME:$IMAGE_VERSION

        echo "Listing Docker images"
        docker image ls

        echo "Pushing the image to GHCR: $REGISTRY/intacct/$TARGET_REPO/$IMAGE_NAME:$IMAGE_VERSION"
        docker push $REGISTRY/intacct/$TARGET_REPO/$IMAGE_NAME:$IMAGE_VERSION
