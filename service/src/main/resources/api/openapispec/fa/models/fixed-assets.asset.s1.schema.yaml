title: asset
x-mappedTo: fixedasset
type: object
description: A resource used in the operations of a business.
properties:
  key:
    x-mappedTo: key
    type: string
    readOnly: true
    description: System-assigned unique key for the asset.
    example: '2'
  id:
    x-mappedTo: id
    x-mutable: false
    type: string
    description: ID for the asset.
    example: 'CE_ASSET-1'
  href:
    x-mappedTo: __framework__
    type: string
    readOnly: true
    description: URL endpoint for the asset.
    example: /objects/fixed-assets/asset/2
  name:
    x-mappedTo: name
    type: string
    description: Name for the asset.
    example: Laptop
  state:
    x-mappedTo: state
    enum:
      - "inService"
      - "readyForReview"
      - "disposed"
    type: string
    default: "readyForReview"
    description: |-
      Represents stages in the asset life cycle:
      
      - `readyForReview` - new asset exists in Sage Intacct but it is not yet depreciating.
      - `inService` - the asset is currently in use by a business and its value can depreciate on a schedule.
      - `disposed` - the asset was removed from service, typically due to a sale.
      
      Assets are moved from `readyForReview` to `inService` and ultimately to `disposed` state.
    example: inService
  assetCost:
    x-mappedTo: assetCost
    type: string
    description: |-
      The amount that the asset was purchased for. It must be a positive number.

      This property is required for assets in service. It is also required if there is a salvage value.
    example: '3000'
  salvageValue:
    x-mappedTo: salvageValue
    type: string
    description: The estimated value of the asset at the end of its useful life. It must be less than the asset cost.
    example: '300'
  inServiceDate:
    x-mappedTo: inServiceDate
    type: string
    format: date
    description: The date when the asset is available for use. This date, along with the convention, determines when the asset starts to depreciate. The in-service date must be on or after the acquisition date.
    example: '2020-01-01'
  serialNumber:
    x-mappedTo: serialNumber
    type: string
    description: Product serial number of the asset.
    example: 'CE_ASSET-1'
  assetTag:
    x-mappedTo: assetTag
    type: string
    description: Product asset tag for the asset.
    example: 'ASSET_TAG-0001'
  multiEntityLocation:
    x-object: "company-config/location"
    x-mappedTo: meLocation
    type: object
    description: The location of the entity associated with the asset.
    properties:
      key:
        x-mappedTo: meLocationKey
        type: string
        readOnly: true
        description: System-assigned key for the location.
        example: '1'
      id:
        x-mappedTo: meLocation.id
        type: string
        readOnly: true
        description: ID for the location.
        example: '1'
      name:
        x-mappedTo: meLocation.name
        type: string
        readOnly: true
        description: Name for the location.
        example: United States of America
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the location.
        example: /objects/company-config/location/1
  attachment:
    x-object: "company-config/attachment"
    x-mappedTo: attachment
    type: object
    description: Supporting document associated with the asset.
    properties:
      key:
        x-mappedTo: attachmentKey
        type: string
        description: System-assigned key for the attachment.
        example: '1'
      id:
        x-mappedTo: attachment.id
        type: string
        description: ID for the attachment.
        example: 'Jan23Xls'
      name:
        x-mappedTo: attachment.name
        type: string
        readOnly: true
        description: Name for the attachment.
        example: January spreadsheet
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the attachment.
        example: /objects/company-config/attachment/1
  quantity:
    x-mappedTo: quantity
    type: integer
    default: 1
    description: Number of units of the asset.
    example: 1
  acquisitionDate:
    x-mappedTo: acquisitionDate
    type: string
    format: date
    description: The date the asset was purchased or obtained by your business.
    example: '2020-01-01'
  description:
    x-mappedTo: description
    type: string
    description: Description of the asset.
    example: Lenovo ThinkPad
  notes:
    x-mappedTo: notes
    type: string
    description: Notes or comments about the asset.
    example: Next major service due at 3,000 hours
  isDepreciable:
    x-mappedTo: depreciate
    type: boolean
    default: true
    description: |-
      Set to `true` if the cost of the asset will be allocated over time. Depreciation schedules are generated when the asset is placed in service.

      Set to `false` for tracking non-depreciable assets such as land, art, coins, or collectibles. No depreciation schedules will be generated.

      If the asset is not depreciable, do not specify the following properties:

      - `salvageValue`
      - `accumulatedDepreciationGLAccount`
      - `depreciationExpenseGLAccount`
      - `depreciationRules`
    example: true
  disposalDate:
    x-mappedTo: disposalDate
    type: string
    format: date
    description: |-
      The date the asset was removed from business operations. 
      
      This date also serves as the General Ledger posting date for the disposal journal entry. The disposal date must be after the last posted period date and before the next un-posted period date. If no periods are posted yet, it must be after the in-service date and before the first period's posting date.
    example: '2022-01-01'
  disposalType:
    x-mappedTo: disposalType
    enum:
      - "sale"
      - "casualtyLoss"
      - "theft"
      - "other"
      - null
    type: string
    default: null
    nullable: true
    description: |-
      The reason for disposal of the asset.

      - `sale` - you receive a payment in exchange for the asset.
      - `casualtyLoss` - the asset is destroyed or damaged due to an unforeseen event or accident. No payment is received.
      - `theft` - the asset is stolen. No payment is received.
      - `other` - the asset is removed from service for any other reason. The asset might still be in your possession, but it reached the end of its useful life or has no resale value. No payment is received.

      If the reason for disposal is `sale`, then the `salePrice` and `disposalGLAccount` properties must be specified.
    example: sale
  salePrice:
    x-mappedTo: salePrice
    type: string
    description: The amount the asset was sold for. Specify for assets with `disposalType` set to `sale`.
    example: '1000'
  disposalComments:
    x-mappedTo: disposalComments
    type: string
    description: Notes about the disposal of the asset.
    example: Sold to Lincoln Avenue Veterinary Clinic
  type:
    x-mappedTo: type
    enum:
      - "tangible"
      - "intangible"
    type: string
    default: "tangible"
    description: |-
      The type of the asset.
      
      Tangible assets have physical substance, such as equipment or land.
      Intangible assets do not have physical substance, such as patents or copyrights.
    example: tangible
  source:
    type: object
    description: The bill the asset was created from.
    properties:
      headerType:
        x-mappedTo: sourceHeaderType
        type: string
        description: Type for the source header.
        example: 'IA.BILL'
      headerKey:
        x-mappedTo: sourceHeaderKey
        type: string
        description: Key for the source header.
        example: '3874'
      headerId:
        x-mappedTo: sourceHeaderId
        type: string
        description: ID for the source header.
        example: 'Bill-00302'
      headerLabel:
        x-mappedTo: sourceHeaderLabel
        type: string
        readOnly: true
        description: Label for the source header.
        example: 'Bill Bill-00302'
      lineKey:
        x-mappedTo: sourceLineKey
        type: string
        description: Key for the source line.
        example: '89483'
      lineNumber:
        x-mappedTo: sourceLineNo
        type: string
        description: Source line number.
        example: '3'
  depreciableCost:
    x-mappedTo: depreciableCost
    type: string
    readOnly: true
    description: The depreciable cost of the asset.
    example: '2700'
  gainLossAmount:
    x-mappedTo: ndGainLossAmount
    type: string
    description: Amount of gain or loss when the asset is disposed for non-depreciable assets.
    example: "300"
  disposedJournalEntry:
    x-object: "general-ledger/journal-entry"
    x-mappedTo: ndDisposedGLBatch
    type: object
    readOnly: true
    description: Disposed journal entry for a non-depreciable asset.
    properties:
      key:
        x-mappedTo: ndDisposedGLBatchKey
        type: string
        readOnly: true
        description: System-assigned unique key for the disposed journal entry.
        example: '6725'
      id:
        x-mappedTo: ndDisposedGLBatch.id
        type: string
        readOnly: true
        description: ID for the disposed journal entry.
        example: '6725'
      txnNumber:
        x-mappedTo: ndDisposedGLBatch.txnNumber
        type: integer
        description: System-assigned transaction number for the disposed journal entry.
        example: 40
        readOnly: true
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the journal entry.
        example: /objects/general-ledger/journal-entry/6725
  journal:
    x-object: "general-ledger/journal"
    x-mappedTo: ndJournal
    type: object
    description: |-
      The journal where transactions related to non-depreciable assets are recorded. Use an active and non-statistical journal.
    properties:
      key:
        x-mappedTo: ndJournalKey
        type: string
        description: Unique key for the journal.
        example: '46'
      id:
        x-mappedTo: ndJournal.id
        type: string
        description: ID for the journal.
        example: TAX
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the journal.
        example: /objects/general-ledger/journal/46
      name:
        x-mappedTo: ndJournal.name
        type: string
        readOnly: true
        description: Name for the journal.
        example: TAX Accrual
      bookId:
        x-mappedTo: ndJournal.bookId
        type: string
        readOnly: true
        description: ID for the book this journal belongs to.
        example: TAXADJACCRUAL
  allocation:
    x-object: "general-ledger/txn-allocation-template"
    x-mappedTo: allocation
    type: object
    description: Allocation entry associated with the asset.
    properties:
      key:
        x-mappedTo: allocationKey
        type: string
        description: System-assigned unique key for the allocation entry.
        example: '1'
      id:
        x-mappedTo: allocation.id
        type: string
        description: ID for the allocation entry.
        example: '1'
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the allocation entry.
        example: /objects/general-ledger/txn-allocation-template/1
  dimensions:
    type: object
    description: |-
      Use dimensions to assign the asset to a project, department, or any other dimension value.
      
      The `location` dimension is always required. Other dimensions might be required depending on the configuration of your accumulated depreciation and depreciation expense GL accounts.
    properties:
      class:
        x-object: "company-config/class"
        x-mappedTo: class
        type: object
        nullable: true
        description: The class associated with this asset.
        properties:
          key:
            x-mappedTo: classDimKey
            type: string
            nullable: true
            description: System-assigned key for the class.
            example: '1'
          id:
            x-mappedTo: classDim.id
            type: string
            nullable: true
            description: ID for the class.
            example: '1'
          name:
            x-mappedTo: classDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the class.
            example: '1'
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the class.
            example: /objects/company-config/class/1
      contract:
        x-object: "contracts/contract"
        x-mappedTo: contract
        type: object
        nullable: true
        description: The contract associated with the asset.
        properties:
          key:
            x-mappedTo: contractDimKey
            type: string
            nullable: true
            description: System-assigned key for the contract.
            example: '1'
          id:
            x-mappedTo: contractDim.id
            type: string
            nullable: true
            description: ID for the contract.
            example: '1'
          name:
            x-mappedTo: contractDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the contract.
            example: '1'
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the contract.
            example: /objects/contracts/contract/1
      costType:
        x-object: "construction/cost-type"
        x-mappedTo: costType
        type: object
        nullable: true
        description: The cost type for the asset.
        properties:
          key:
            x-mappedTo: costTypeDimKey
            type: string
            nullable: true
            description: System-assigned key for the cost type.
            example: '7'
          id:
            x-mappedTo: costTypeDim.id
            type: string
            nullable: true
            description: ID for the cost type.
            example: LABOR
          name:
            x-mappedTo: costTypeDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the cost type.
            example: LABOR
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the cost type.
            example: /objects/construction/standard-cost-type/7
      customer:
        x-object: "accounts-receivable/customer"
        x-mappedTo: customer
        type: object
        nullable: true
        description: The customer associated with the asset.
        properties:
          key:
            x-mappedTo: customerDimKey
            type: string
            nullable: true
            description: System-assigned key for the customer.
            example: '1'
          id:
            x-mappedTo: customerDim.id
            type: string
            nullable: true
            description: ID for the customer.
            example: '1'
          name:
            x-mappedTo: customerDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the customer.
            example: Power Aerospace Materials
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the customer.
            example: /objects/accounts-receivable/customer/1
      department:
        x-object: "company-config/department"
        x-mappedTo: department
        type: object
        nullable: true
        description: The department associated with the asset.
        properties:
          key:
            x-mappedTo: departmentDimKey
            type: string
            nullable: true
            description: System-assigned key for the department.
            example: '3'
          id:
            x-mappedTo: departmentDim.id
            type: string
            nullable: true
            description: ID for the department.
            example: '3'
          name:
            x-mappedTo: departmentDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the department.
            example: Engineering
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the department.
            example: /objects/company-config/department/3
      employee:
        x-object: "company-config/employee"
        x-mappedTo: employee
        type: object
        nullable: true
        description: The employee associated with the asset.
        properties:
          key:
            x-mappedTo: employeeDimKey
            type: string
            nullable: true
            description: System-assigned key for the employee.
            example: '1'
          id:
            x-mappedTo: employeeDim.id
            type: string
            nullable: true
            description: ID for the employee.
            example: '1'
          name:
            x-mappedTo: employeeDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the employee.
            example: Amy Nguyen
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the employee.
            example: /objects/company-config/employee/1
      item:
        x-object: "inventory-control/item"
        x-mappedTo: item
        type: object
        nullable: true
        description: The item associated with the asset.
        properties:
          key:
            x-mappedTo: itemDimKey
            type: string
            nullable: true
            description: System-assigned key for the item.
            example: '1'
          id:
            x-mappedTo: itemDim.id
            type: string
            nullable: true
            description: ID for the item.
            example: '1'
          name:
            x-mappedTo: itemDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the item.
            example: PC Computer
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the item.
            example: /objects/inventory-control/item/1
      location:
        x-object: "company-config/location"
        x-mappedTo: location
        type: object
        description: The location associated with the asset.
        properties:
          key:
            x-mappedTo: locationDimKey
            type: string
            nullable: true
            description: System-assigned key for the location.
            example: '1'
          id:
            x-mappedTo: locationDim.id
            type: string
            nullable: true
            description: ID for the location.
            example: '1'
          name:
            x-mappedTo: locationDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the location.
            example: United States of America
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the location.
            example: /objects/company-config/location/1
      project:
        x-object: "projects/project"
        x-mappedTo: project
        type: object
        nullable: true
        description: The project associated with the asset.
        properties:
          key:
            x-mappedTo: projectDimKey
            type: string
            nullable: true
            description: System-assigned key for the project.
            example: '8'
          id:
            x-mappedTo: projectDim.id
            type: string
            nullable: true
            description: ID for the project.
            example: '8'
          name:
            x-mappedTo: projectDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the project.
            example: Client Services - Power Aerospace Materials
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the project.
            example: /objects/projects/project/8
      task:
        x-object: "projects/task"
        x-mappedTo: task
        type: object
        nullable: true
        description: The task associated with the asset.
        properties:
          key:
            x-mappedTo: taskDimKey
            type: string
            nullable: true
            description: System-assigned key for the task.
            example: '8'
          id:
            x-mappedTo: taskDim.id
            type: string
            nullable: true
            description: ID for the task.
            example: '01-041'
          name:
            x-mappedTo: taskDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the task.
            example: Project coordination
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the task.
            example: /objects/projects/task/8
      vendor:
        x-object: "accounts-payable/vendor"
        x-mappedTo: vendor
        type: object
        nullable: true
        description: The vendor associated with the asset.
        properties:
          key:
            x-mappedTo: vendorDimKey
            type: string
            nullable: true
            description: System-assigned key for the vendor.
            example: '43'
          id:
            x-mappedTo: vendorDim.id
            type: string
            nullable: true
            description: ID for the vendor.
            example: '1099 Int'
          name:
            x-mappedTo: vendorDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the vendor.
            example: '1099 Int'
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the vendor.
            example: /objects/accounts-payable/vendor/43
      warehouse:
        x-object: "inventory-control/warehouse"
        x-mappedTo: warehouse
        type: object
        nullable: true
        description: The warehouse associated with the asset.
        properties:
          key:
            x-mappedTo: warehouseDimKey
            type: string
            nullable: true
            description: System-assigned key for the warehouse.
            example: '1'
          id:
            x-mappedTo: warehouseDim.id
            type: string
            nullable: true
            description: ID for the warehouse.
            example: 'WH01'
          name:
            x-mappedTo: warehouseDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the warehouse.
            example: WH01 Lakefront
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the warehouse.
            example: /objects/inventory-control/warehouse/1
      affiliateEntity:
        x-object: "company-config/affiliate-entity"
        x-mappedTo: affiliateEntity
        type: object
        nullable: true
        description: The affiliate-entity associated with the asset.
        properties:
          key:
            x-mappedTo: affiliateEntityDimKey
            type: string
            nullable: true
            description: System-assigned key for the affiliate-entity.
            example: '1'
          id:
            x-mappedTo: affiliateEntityDim.id
            type: string
            nullable: true
            description: ID for the affiliate-entity.
            example: '1'
          name:
            x-mappedTo: affiliateEntityDim.name
            type: string
            nullable: true
            readOnly: true
            description: Name for the affiliate-entity.
            example: United States of America
          href:
            x-mappedTo: __framework__
            type: string
            nullable: true
            readOnly: true
            description: URL endpoint for the affiliate-entity.
            example: /objects/company-config/affiliate-entity/1
  assetGLAccount:
    x-object: "general-ledger/account"
    x-mappedTo: assetAcct
    type: object
    description: Required if `state` is set to `inService`. The General Ledger account where transactions involving asset cost and disposal are recorded as debits and credits.
    properties:
      key:
        x-mappedTo: assetAcctKey
        type: string
        description: System-assigned key for the asset GL account.
        example: '93'
      id:
        x-mappedTo: assetAcct.id
        type: string
        description: ID for the asset GL account.
        example: '1500'
      name:
        x-mappedTo: assetAcct.name
        type: string
        readOnly: true
        description: Name for the asset GL account.
        example: Computer Equipment
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the asset GL account.
        example: /objects/general-ledger/account/93
  accumulatedDepreciationGLAccount:
    x-object: "general-ledger/account"
    x-mappedTo: deprAcct
    type: object
    description: Use only if `isDepreciable` is set to `true`. Required if `state` is set to `inService`. The General Ledger account where depreciation transactions are recorded as credits. It offsets the depreciation expense account.
    properties:
      key:
        x-mappedTo: deprAcctKey
        type: string
        description: System-assigned key for the accumulated depreciation GL account.
        example: '98'
      id:
        x-mappedTo: deprAcct.id
        type: string
        description: ID for the accumulated depreciation GL account.
        example: '1901'
      name:
        x-mappedTo: deprAcct.name
        type: string
        readOnly: true
        description: Name for the accumulated depreciation GL account.
        example: Accumulated Depreciation
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the accumulated depreciation GL account.
        example: /objects/general-ledger/account/98
  depreciationExpenseGLAccount:
    x-object: "general-ledger/account"
    x-mappedTo: deprExpAcct
    type: object
    description: Use only if `isDepreciable` is set to `true`. Required if `state` is set to `inService`. The General Ledger account where depreciation transactions are recorded as debits.
    properties:
      key:
        x-mappedTo: deprExpAcctKey
        type: string
        description: System-assigned key for the depreciation expense GL account.
        example: '270'
      id:
        x-mappedTo: deprExpAcct.id
        type: string
        description: ID for the depreciation expense GL account.
        example: '6350'
      name:
        x-mappedTo: deprExpAcct.name
        type: string
        readOnly: true
        description: Name for the depreciation expense GL account.
        example: Depreciation Expense
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the depreciation expense GL account.
        example: /objects/general-ledger/account/270
  disposalGLAccount:
    x-object: "general-ledger/account"
    x-mappedTo: disposalAcct
    type: object
    description: |-
      The General Ledger account where the sales price is recorded as a debit. Typically, it is a depreciation expense account or a gain and loss account. 
      
      Specify this property when the `disposalType` is set to `sale`. Required when the `salesPrice` is greater than zero.
      
      The account must be active and non-statistical. Specify a different account than the `gainLossGLAccount`.
    properties:
      key:
        x-mappedTo: disposalAcctKey
        type: string
        description: System-assigned key for the disposal GL account.
        example: '101'
      id:
        x-mappedTo: disposalAcct.id
        type: string
        description: ID for the disposal GL account.
        example: '1904'
      name:
        x-mappedTo: disposalAcct.name
        type: string
        readOnly: true
        description: Name for the disposal GL account.
        example: Computer Equipment
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the disposal GL account.
        example: /objects/general-ledger/account/101
  gainLossGLAccount:
    x-object: "general-ledger/account"
    x-mappedTo: gainLossAcct
    type: object
    readOnly: true
    description: |-
      The General Ledger account where the gain or loss on disposal amount is recorded as a debit or credit.

      The account must be active and non-statistical. Specify a different account than the `disposalGLAccount`.
    properties:
      key:
        x-mappedTo: gainLossAcctKey
        type: string
        readOnly: true
        description: System-assigned key for the gain loss GL account.
        example: '94'
      id:
        x-mappedTo: gainLossAcct.id
        type: string
        readOnly: true
        description: ID for the gain loss GL account.
        example: '1501'
      name:
        x-mappedTo: gainLossAcct.name
        type: string
        readOnly: true
        description: Name for the gain loss GL account.
        example: Gain account
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the gain loss GL account.
        example: /objects/general-ledger/account/94
  classification:
    x-object: "fixed-assets/asset-classification"
    x-mappedTo: classification
    type: object
    description: |-
      Required if `state` is set to `inService`. Provides the GL accounts and depreciation rules properties with default values for assets of that category.
      
      You can customize the GL accounts and depreciation rules per asset, after the asset has been created.
    properties:
      key:
        x-mappedTo: classificationKey
        type: string
        description: System-assigned key for the classification.
        example: '3'
      id:
        x-mappedTo: classification.id
        type: string
        description: ID for the classification.
        example: Computer Equipment
      name:
        x-mappedTo: classification.name
        type: string
        readOnly: true
        description: Name for the classification.
        example: 'CE-1'
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the classification.
        example: '/objects/fixed-assets/classification/3'
  parent:
    x-object: "fixed-assets/asset"
    x-mappedTo: parent
    type: object
    description: By linking parent assets, you can create a hierarchy of assets. An asset can only have one parent. An asset cannot reference itself as the parent.
    properties:
      key:
        x-mappedTo: parentKey
        type: string
        description: System-assigned key for the parent asset.
        example: '1'
      id:
        x-mappedTo: parent.id
        type: string
        description: ID for the parent asset.
        example: 'CE_ASSET-0'
      name:
        x-mappedTo: parent.name
        type: string
        readOnly: true
        description: Name for the parent asset.
        example: Lenovo
      href:
        x-mappedTo: __framework__
        type: string
        readOnly: true
        description: URL endpoint for the parent asset.
        example: /objects/fixed-assets/asset/1
  depreciationRules:
    x-object: "fixed-assets/asset-depreciation-rule"
    x-mappedTo: deprRules
    type: array
    description: |-
      Depreciation rules contain information about the depreciation method, convention, and useful life for the asset.
      
      If the asset `state` is set to `inService` and `isDepreciable` is set to `true`, it must have at least one depreciation rule. 
      Assets that are not depreciable cannot have any depreciation rules.
    items:
      $ref: "fixed-assets.asset-depreciation-rule.s1.schema.yaml"
  transferDate:
    x-mappedTo: transferDate
    type: string
    writeOnly: true
    format: date
    description: |-
      The latest date the asset was transferred. 

      This date also serves as the General Ledger posting date for the transfer journal entry in transfer-history object.
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
