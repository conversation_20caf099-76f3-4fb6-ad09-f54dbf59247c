spring:
  application:
    name: @project.name@
com:
  intacct:
    version: @project.version@
    pt:
      id-prefix: 3
    core:
      legacy-update-interface: false
      offline-jobs: "enabled"
      #sandbox-namespace: ${SANDBOX_NS:test4fa}
      api:
        client:
          remote-service:
            logging-level: none # replace 'none' by 'full' to get details about error conditions
    test:
      bdd:
        dev-version: "0-beta2"
    hlth:
      db-schemas: disabled
    ds:
      ia-ds-php:
        url: ${NEXTGEN_REMOTE_SERVER:https://pod-p308.intacct.com/users/jenkins/main}
      # Enable the following 2 service settings to test FA provisioning with the K8s dev instances
      #ia-ds-iodl:
      #  url: http://localhost:8090/ia-ds-iodl/ia # this line is for local deployment when using sandbox-namespace:
      #  url: https://ia-ds.dev01.us-west-2.dev.ds-dev.intacct.com/ia-ds-iodl/ia
      #  sandbox: ia_core_test_iodl_provisioning
      #ia-ds-text:
      #  remote-access:
      #    enabled: true
      #  url: https://ia-ds-dc308.dev.ds.intacct.com/ia-ds-text/ia
test:
  core:
    unit-test:
      # extra properties required to test locally against a p307 instance
      #client-id: "92d4dcf414e6a2c3d78d.Sage_Intacct_UI.app.sage.com"
      #client-secret: "a55a58f1aeaf09116cbe1bf28025c183e778268c"
      company: "famtony1"
      company-name: "famtony1 cny# = 30801045243860"
    bdd:
      #       set BDD test version to align with the registry version generated for this app
      #       the following example is assuming the app still generating RegistryV0-beta2.json for the purpose of on-par with ia-app
      dev-version: "0-beta2"
      default-company: "Admin@famtony1"
      companies:
        unit-test-default:
          title: famtony1
          user: Admin
          password: Aa123456!
        unit-test-ap:
          title: famunittestap
          user: Admin
          password: Aa123456!
        unit-test-ap-aman:
          title: famunittestap
          user: Aman
          password: Aa123456!
        unit-test-disposal:
          title: PK_FAM_UT_02
          user: Admin
          password: Aa123456!
        unit-test-new:
          title: fam_ut_new
          user: Admin
          password: Aa123456!
