# ia-ds-fa

## Requirements for running the project

Optional package managers to help installing dependencies:

* [Chocolatey package manager](https://chocolatey.org/install) - windows only
* [brew package manager](https://brew.sh) - macOS

Required software:

* Java 17 - Liberica 17.0.X full - we are running on version ******** 
* Maven
* [Skaffold package](https://community.chocolatey.org/packages/skaffold)
* Docker desktop
* Kubernetes cluster ( docker desktop can be used until EOY 2021)
* metrics server deployed on the cluster [instructions here](https://github.com/kubernetes-sigs/metrics-server)
* openapi-generator-cli (only for api generation )
    - [official instructions](https://openapi-generator.tech/docs/installation)

## Project contents - dependencies

* ia-core-fmwk
* Spring Boot, Spring boot starter actuator / web / devtools / test
    * web MVC used as application container
    * actuator for probes, metrics, jmx, etc.
    * devtools for live class reloading in debug
    * test standard Junit 5 test runner
* Spring cloud kubernetes client
    * kubernetes API interaction - used for pulling configuration from configMaps and secrets; discovery should be
      disabled
* Lombok
* Jolokia
    * enable JMX over HTTP through actuator
* SpringDoc
    * OpenAPI 3 documentation for exposed APIs
* Micrometer
    * metrics framework to expose through actuator or push to metric server (depending on target metric platform)
* k8s config files `k8s/config.yml`, `k8s/deploy.yml`
* skaffold config file `skaffold.yml`
* dummy endpoint `http://localhost:8090/dummy`

## Integration testing

Before to run these suites, you need to have installed newman (the CLI companion for Postman) and you need to have an 
instance running on localhost.

```shell
cd src/test/newman
newman run fa-v0-asset.Collection.json -e <EMAIL> -n 1 --delay-request 1
newman run fa-v0-classification.Collection.json -e <EMAIL> -n 1 --delay-request 1
```
When you run newman command with the option -r htmlextra, there is nothing printed on the console output during the run.
Instead, at the end of the run, you will see the extra folder created - the default name is `newman`. You can open the 
generated report with your browser of chose. The name of the file will include the timestamp of that run.

You can also use the `--verbose` option to get more details in the output. Here is a sample command used to run the 
sanity suite using our K8s instance on `pod1`.

```shell
newman run fa-v0-POCKongK8s-PodSanityTesting.Collection.json -e FA-Pod1-Kong_K8s.Environment.json -n 1 --delay-request 1 --verbose
```

see the [Newman Setup](https://intacct.atlassian.net/wiki/spaces/DEV/pages/2371027412/Executing+Postman+collections+from+command+line) 
page for details.

We can also go to the [CI page](https://usw-ci-m01-stg.intacct-dev.com/view/Fixed%20Assets/view/FA%20-%20Smoke%20Sanity%20-%20Dev24%20QA03%20Trunk/) 
on our Jenkins server.

The value to use for local runs is `fa/v0/<EMAIL>`

## Todos

* add CI to render and push images
* set up configurable container registry so that ops/devops can choose where images are stored depending on usage
* bootstrap.sh equivalent for windows
* move JAVA_TOOL_OPTIONS from k8s manifest to buildpack parameter - it should be baked in at assembly, not runtime

* remember to run the push only once at a time ...

## Steps to start from the template

### forking git repo and renaming application

all occurrences of ds-fa or ds application should be replaced to match the new domain service name:

* `spring.messages.basename` should be filled with a comma separated list of paths to internationalized messages (if you
  have a `langFolder` like described in the next bullet, then you should add
  `langFolder.messages`; also, if you want to use messages from
  `ia-core-framework`, then you should add `lang.messages` since they are stored in `lang` folder)
* src/main/resources/langFolder -> `langFolder` can be renamed according to user preferences; it is the folder that
  points to the bundle of `messages.properties` files
  (coupled to the internationalized messages used in the project); the folder should contain files with different
  endings (`_en`, `_de`, `_fr`,
  `_ro` etc.) that express messages in different languages
* k8s/*.yaml - ALL occurrences. The Deployment, Horizontal Pod Autoscaler, Service manifests (names + image references)
  should be unique to the application and consistent. The config map should have the same name
  as `spring.application.name`

### Skaffold

Official docs [here](https://skaffold.dev/docs/)

The fastest way to get the app running is by executing `skaffold dev --port-forward` in a terminal. This option enables
continuous local development where changing a file will trigger an image build.

For debugging, we will use [Cloud Code](https://plugins.jetbrains.com/plugin/8079-cloud-code) plugin for Intellij. Select
the "Develop on Kubernetes" run configuration and hit the Debug button. Similary hitting the run option will start the
application in continuous local development mode.

#### skaffold.yaml contents

The template file specifies the following:

* metadata - name of the application
* tag policy is set up to use tags and will fall back to commit id if not on a tag
    * skaffold 'hydrates' the manifest with the generated image name: so if you
      see `374322211295.dkr.ecr.us-west-2.amazonaws.com/ia-ds-fa/ia-ds-fa` in the k8s manifests, skaffold will generate
      an image, tag it with the commit id and update the manifest for deployment (render phase)
    * typical image naming: `registry/image:<tag/commit id>[-dirty]` where:
        * registry and image name should be set up before development and kept in sync with ./k8s/*.yaml contents
        * tag or commit id is chosen based on the current git status
        * the optional `-dirty` suffix is added when there are uncommitted changes in the repo
* images section defines the OCI artifacts the project produces and the recipes to build them. In our case it's just one
  image:
    * ds-fa image definition
    * pre-build hook is needed to populate a container volume with the maven authentication details.
      See: `./bootstrap.sh`
    * build section defines how the image is built: in our case using base paketo buildpack, with the volume generated
      at the prior step mounted for maven auth. The buildpack runs with default settings, hence it will have a jar
      launcher helper that computes memory spit for current # of classes, an assumed 250 threads. The memory ceiling is
      set up as equal to the container's max resources
* local section define parameters for local builds. Current setup is to optimize build speed on docker using buildkit
* the deployment section defines all kubernetes manifests to scan, render and apply

### k8s/ folder manifests

This folder contains the 2 manifest files:

* deployment.yml - contains deployment spec for the application, along with HPA and service
    * Deployment - specifies the pod spec with 1 container for our application. additional settings include exposed
      ports, health probes and resource specifications.\
      Note that for java applications memory request and limit should be equal. The GC will not relinquish memory back
      to the OS.
    * HorizontalPodAutoscaler - specifies the pod horizontal scaling policy. Currently, set to aim for 3vCPU used and
      scale between 1 and 3 replicas, as a sample.
    * Service - defines a k8s address to access our services (DNS entry), and exposes the application port as
      ClusterIP (internal ip in the k8s cluster)
* config.yml - configmap to override application settings. It accepts property style fields or full application.yml
  files. [more info](https://docs.spring.io/spring-cloud-kubernetes/docs/current/reference/html/#kubernetes-propertysource-implementations)

### General development workflow:

The application template should run both straight out of maven for tests or just spring-boot:run, but the usual workflow
should be through skaffold dev or debug. Development should not need any skaffold phases outside build, dev and run, but
generally dev should be used.

## API client generation:

In order to circumvent a direct framework/jar dependency between projects and force us into complicating release
management, we're offering the possibility to generate the rest clients
using [OpenApi generator](https://openapi-generator.tech/).

The files required to generate clients are:

* /target/ia-resources/openapi.json - produced with springdoc maven plugin during integration-test phase of the build
* /etc/client_gen/ - contains scripts, templates and configuration to generate clients
    * feign/ - folder containing the custom openapi-generator templates for spring cloud openfeign
    * ia-feign.yml - configuration file we use for openapi-generator with our default parameters
    * generate_client_files.sh - bash script to generate client (if no parameters specified, it uses defaults)
* /generated_client - target folder including a .ignore file specific to openapi generator

In order to produce the openapi spec and Intacct resources in ```${project.build.directory}/ia-resources```
the ```gen-ext-resources``` profile must be active

The generator will produce a complete file structure in the generated_client folder, containing all the api interfaces,
a feing client configuration with Java11 http client set up, as well as all the model classes as defined in the api
spec. Consequently, you should see a folder structure similar to the following output:

```
└── src
    └── main
        └── java
            └── com
                └── intacct
                    └── ds
                        └── client
                            └── dummy
                                ├── api
                                │   ├── DummyControllerApi.java
                                │   └── DummyControllerApiClient.java
                                ├── conf
                                │   └── ClientConfiguration.java
                                └── model
                                    ├── AccountType.java
                                    ├── Contact.java
                                    ├── Dummy.java
                                    ├── GLAccount.java
                                    ├── MailAddress.java
                                    ├── SOPriceList.java
                                    ├── SalePurchaseType.java
                                    ├── StatusType.java
                                    ├── TaxGroup.java
                                    ├── TaxGroupType.java
                                    └── TaxSolution.java

11 directories, 15 files
```

#### Client code structure:

There are 3 main sets of files generated:

* api - contains an interface with methods corresponding to the REST endpoints defined in the spec, and its paired
  ApiClient class which is annotated with ```@FeignClient```, with configurable name, url and pre-set configuration
  class. It should be ready to be ```@Autowired``` as is
* conf - contains the client specific configuration class. Given our requirement for PATCH verbs, a Client bean has been
  set up from the template to use the Java11 HttpClient
* model - all the model classes as defined in the api spec. These are already set up with Jackson annotations. They
  should be ready to use as is, but they can be Lombok'd in IntelliJ easily if needed.

All these files are generated in packages specified via config ( defaulted in the script ), and can be overridden with
script parameters.

#### Generating feign client code

Scripts must be run from the /etc/client_gen folder:

Running ```./generate_client_files.sh```:

The script can be run as is and will generate the three packages under ```com.intacct.ds.client```
In order to customize the api, model and conf packages.

```shell
./generate_client_files.sh -a com.intacct.ds.client.dummy.api -m com.intacct.ds.client.dummy.model -c com.intacct.ds.client.dummy.conf -s ../../target/ia-resources/openapi.json
```

openapi-generator should be able to work with remote api specs in the form of url to the -s parameter of the script, but
it was not tested at the time of writing.

#### Using the output

After the generation, the files can be copied directly over the service code, similar to doing a
```cp ./generated_client/src/* ../../src/```
. This is not done automatically since we want to give devs a chance to review; also, the aim is to pick up an api spec
from an existing service and generate the client locally and use it in another ds service. The code should already be
organized in the right structure, and should not overwrite existing code. (adjust via script params as needed)
The whole process is based on a "share-nothing" approach commonly used in microservices, so each client of service A
will have its copy of the model beans. This is done to prevent breaking changes on API changes.

#### Details on the default settings and how it's set up:

We are using locally kept feign templates to allow customizations specific to our usage, such as the Java11 http client
for starters. In order to have them run this way, we currently had to duplicate even the unmodified templates in
/etc/client_gen/feign. In order to edit the templates, these are mustache files and documentation/samples is available
in the openapi-gen [customization docs](https://openapi-generator.tech/docs/templating) as well as
their [GitHub repo](https://github.com/OpenAPITools/openapi-generator)
Our defaults:
/etc/client_gen/ia-feign.yml is set up to produce clients as close as possible to the shape we want:

- spring cloud feign, not openfeign directly
- using java8 datetime libraries
- jackson serialization is implicit
- avoid using swagger/openapi/Jackson annotations for nullable, etc.
- generate Java validation annotations, but do not force validation. In the future we'll be able to validate beans vs
  schema.

### Remaining improvements:

* GitHub action to produce openapi spec and resources as artifact
* adapt Nullable and other contract specifications to our needs
* move to either .jar openapi generator or docker, in order to lock in the version
* TBD: package generator as a build artifact, so it's "runnable" without having the whole repo.

## CI

At this step we will set up the CI jobs for our domain service project. There are 2 types of jobs that we are setting
up:

1. PR and branch level job. The job will build (and test) each PR submitted. It is also enabled on branches, but the
   Jenkinsfile is a no-op on branches for now.
2. Main/release level job. This job will build after each commit is pushed to main. It will generate a release-level
   artifact in artifactory, and it will increment the build number in the snapshot version in main.

* Navigate to https://ci-ds.intacct.com/view/Domain%20Services/view/ia-ds-fa/job/ia-ds-fa/view/change-requests/
* You will find a view named as your GitHub project - for example ```PR-1941``` - not sharing specific URL as they will 
be obsolete by the time you read this. 
* You can also go to the `main` CI - URL: https://ci-ds.intacct.com/view/Domain%20Services/view/ia-ds-fa/job/ia-ds-fa/job/main/


### Monitoring build of our dependencies:
* schedule to run daily at 22:00 (PST) - deployment of the PHP application on

a) jenkins/p10: https://cd.intacct.com/view/Deployment%20Jobs/job/deploy_p10_v2/

b) for the release branch; currently Aug25: https://cd.intacct.com/view/Deployment%20Jobs/job/deploy_aug25_v2/

### Github PRs status checks

At this step we are enabling GitHub to block the PR merges until they are successfully built (and tested) in CI.

* Create a (dummy) PR on your project. Don't approve it or merge it yet.
* Manually run a build for the first job, the one named as your GitHub project. It should find and build the PR.
* Make sure that the job on the PR runs successfully. Troubleshoot as necessary.
* (You might need to require an administrator of your repository to perform this step) Go to Github -> Settings ->
  Branches. Edit the `Branch protectino rules` for `main` branch. Check the
  box `Require status checks to pass before merging` and `Require branches to be up to date before merging`. In the
  search box below these two boxes, find and select the status check named `continuous-integration/jenkins/pr-merge`.
  Save changes.

The two locations to remember when you want to find remote files generated for the PHP side of the house, you can look 
into those two places:
* Remote cache: /chroot/apache/webdirs/ds_cache-refresh
* Sandbox path: app/link/private/ds_generated (every PHP sandbox will point to that shared code for dev instances)

For more information, see https://intacct.atlassian.net/wiki/spaces/DEV/pages/2557214799/Provisioning+from+DS+to+MongoDB+for+PHP

### Manual steps required to test in dev instances
* Run the job to simulate posting: https://p308-web002.intacct.com/users/eric.rajkovic/dev.eric/ops/dispatch-DeprSchRun?.context=famtony1
* Sync the data to display on AuditTrail page: https://p308-web002.intacct.com/users/eric.rajkovic/dev.eric/tools/ops.phtml?action=audit_storage&.migration=0&.imsdbid=dev77&.context=famtony1&.dlog=acct.log.ecco

Reference: [How-to-process-the-Raw-Audit-Trail-Data](https://intacct.atlassian.net/wiki/spaces/AT/pages/331743442/Audit+Trail+-+Developers+Guide#How-to-process-the-Raw-Audit-Trail-Data-(Audit-Storage-Continuous-Migration))

### Deploying on your own sandbox

When you are working on new code, it can be particle to deploy your own PHP sandbox and have it using a modified version
of our JAVA code, which is not yet delivered to main. here are the steps required to do this.
Documentation link: https://intacct-internal-dev-docs.redoc.ly/ds-guide/sandbox-level-testing/

Prerequisites:
* you have a PHP sandbox available
* you have installed helm - I used this one line:
```shell
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
helm repo add ia-helm https://intacct.jfrog.io/artifactory/ia-helm/ --username eric.rajkovic
sudo npm install -g devspace
```

### Testing with a local instance of the ds_cache

It's a two steps process where you need to tweak the JAVA code, then perform a cache refresh on the PHP side.

In your JAVA project edit `service/src/main/resources/application-ds.yml` and add:
```shell
    core:
      sandbox-namespace: ${SANDBOX_NS:test4fa}
...
      ia-ds-iodl:
        url: http://localhost:8090/ia-ds-iodl/ia      
```
start the service and confirm you have the following in the log - search for `EnvPropConfig` and have a line like:
```logcatfilter
timestamp WARN 200 --- [           main] com.intacct.config.EnvPropConfig         : App ia-ds-fa, version 1.1.60-SNAPSHOT, sandbox test4fa
```
then run these commands:
```shell
curl --location --request GET 'http://localhost:8090/ia-ds-fa/ia/deploy/v1/run-task/objectstask' --header 'Content-Type: application/json'
curl --location --request GET 'http://localhost:8090/ia-ds-fa/ia/deploy/v1/run-task/apitask' --header 'Content-Type: application/json'
```
Should get `{"success":true,"messages":null}` x2 in the output - in the console for the running instance, you will be able 
to see the details about the deployment step like
```logcatfilter
timestamp  INFO 72880 --- [nio-8090-exec-1] c.intacct.core.deploy.RegistryService    : >>> Stored registries in MongoDB in collection test4fa::registry::ia-ds-fa in 23840422 nanos <<<
timestamp  INFO 72880 --- [nio-8090-exec-1] c.intacct.core.deploy.RegistryService    : >>> Storing schemas in MongoDB in collection test4fa::schema::ia-ds-fa <<<
...
```

We also need to perform the same command from `ia-ds-iodl` project to get a local copy of the core library in use. There 
is a one-time setup required to get the project loaded into the IDE and to set up so that you can run the application. Once 
this is done, the steps are similar those we have executed above. The key point to remember is that you need to make the 
same change to your `application-ds.yml` in this project, as we did for `ia-ds-fa`.
```shell
cd ~/dev
<NAME_EMAIL>:Intacct/ia-ds-iodl ia-ds-iodl 
cd ia-ds-iodl
git pull
mvn clean ; mvn 
curl --location --request GET 'http://localhost:8090/ia-ds-iodl/ia/deploy/v1/run-task/apitask' --header 'Content-Type: application/json'
```

Then on the PHP side, edit your `ia_init.cfg` file and update the `K8S_SANDBOX` value and re-build (to be safe).
```
K8S_SANDBOX = "test4fa"
```
then run this other curl command:  `curl https://<host>/users/<user>/<pathtosandbox>/ops/ds_cache?operation=refresh&force=true&server=false` - note that you may need to use `"` around the URL to avoid interpretation of `&` in your OS. For me, it was:
```shell
curl "https://www-p308.intacct.com/users/eric.rajkovic/dev.eric/ops/ds_cache?operation=refresh&force=true&server=false"

or

curl "https://www-p310.intacct.com/ia/ops/ds_cache?operation=refresh&force=true&server=false"
```
You will see the files from the cache you have in JAVA copied over to your ~/app/ds_generated folder - it's magic.

## Code

Start by looking at the provided objects implementation:

Classification
* [object definition](service/src/main/resources/objects/fa/classification_object.json)
* [table definition](service/src/main/resources/tables/faclassification_table.json)
* [Classification object DTO](service/src/main/java/com/intacct/ds/fa/model/Classification.java)
* [Classification service](service/src/main/java/com/intacct/ds/fa/service/ClassificationService.java)
* [Classification API schema](service/src/main/resources/api/openapispec/fa/models/fixed-assets.asset-classification.s1.schema.yaml)
* [Classification API schema history](service/src/main/resources/api/openapispec/fa/history/fixed-assets.asset-classification.schema.history.yaml)

Asset
* [object definition](service/src/main/resources/objects/fa/fixedasset_object.json)
* [table definition](service/src/main/resources/tables/faasset_table.json)
* [Asset object DTO](service/src/main/java/com/intacct/ds/fa/model/FixedAsset.java)
* [Asset service](service/src/main/java/com/intacct/ds/fa/service/FixedAssetService.java)
* [Asset API schema](service/src/main/resources/api/openapispec/fa/models/fixed-assets.asset.s1.schema.yaml)
* [Asset API schema history](service/src/main/resources/api/openapispec/fa/history/fixed-assets.asset.schema.history.yaml)

UnitTest
* [Unit test](service/src/test/java/com/intacct/ds/fa/service/AssetServiceTest.java) - [Coverage report](https://ci-ds.intacct.com/job/ia-ds-fa/job/main/lastBuild/jacoco/) you
may have to go to a previous run to see the report as it's only generated on successful builds.

When there is contention on the DB side, some unit tests may hang forever - to resolve this, you can clear the DB lock
using SQLDeveloper (or your alternate tool) - here are the commands, once you have connected to your instance schema:
```sql
select * from table(dbusr.lock_list());

exec dbusr.kill_session(3681, 36177); -- 3681 is the SID, 36177 is the SERIAL#
```
We are using a few companies in our unit tests: `famunittestap`, `fam_ut_new`, `PK_FAM_UT_02` and `famtony1` - if/when
some tests fail, you may have to go cleanup some data left behind from a previous run. Aborting the test execution in 
the middle of the run is one known reason to leave data behind, which should have been dropped as we roll back changes 
at the end of the execution of each test suite.

External dependencies - PHP application:
* localhost:8090          -> https://pod-p308.intacct.com/ia (value from application-ds.yml)
* api-dev.intacct.com     -> https://pod-p307.intacct.com/users/jenkins/p10 
* dc308.ds.intacct.com    -> https://api-p308.intacct.com/users/jenkins/main

### Sync code with IODL repository

We do not need to manually sync the code we develop any longer. In the [IODL git repository](https://github.com/intacct/ia-iodl-ui-config/), 
we only need to deploy both core and fa directories. Here are the commands I am using to perform the deployment:
```shell
cd ~/dev/ia-iodl-ui-config
./deployIODL308.sh fa_eric core < ./enter.key
./deployIODL308.sh fa_eric fa v1 ../ia-ds-fa < ./enter.key
```

### Naming Patterns
As we reference other objects by name in our files, we have to be consistent with the naming pattern we use. Sharing a 
few here to help with identifying them and keeping them in sync, moving forward:
* in *_object.json:
```  
  "object": "journal"
```
* in *.s1.schema.yaml
```
  x-object: "general-ledger/journal"
```
* in *.s1.schema.yaml
```
  x-mappedTo: "GOLIVEDATE"        -- when using the DB field names, we should be using uppercase only
  x-mappedTo: "MODULE_CONFIGURED" -- this one is the exception as we used `_` between words; not our normal practice
```
sample from `app/source/openapispec/fa/models/fixed-assets.asset.s1.schema.yaml`
```  
  deprExpAcct:
    x-object: "general-ledger/account"
    x-mappedTo: "DEPREXPACCTNO"
    type: "object"
```
### Other notes

If you have multiple instances of the JDK install, remember to use the correct version in your setup. There are two 
places where you may need to do the update:
1) JDK used to run from the IDE - Files > Project Structure... > Project > SDK
2) JDK used to run from the CLI - It will be environment specific. For me with a Mac, it was: 
```shell
$ declare -x JAVA_HOME="/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-********"
$ mvn
```
You can check you are using the correct version with this command:
```
$ java --version
openjdk ******** 2023-08-24 LTS
...
```
Simple API to test functionality: obtain a valid OAuth2 code for our API using the linked PHP sandbox, then execute:
```GET http://127.0.0.1:8090/api/v0/objects/classification```. The default PHP sandbox, linked in application-ds.yml,
is ```https://pod-p308.intacct.com/users/jenkins/main```
