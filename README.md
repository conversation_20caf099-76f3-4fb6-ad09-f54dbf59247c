# ReflexAI PR Analyzer

An AI-powered Pull Request analyzer that can analyze GitHub PRs and generate similar code using pattern extraction and AI code generation.

## Features

- **PR Analysis**: Fetches and analyzes GitHub Pull Request data
- **Pattern Extraction**: Extracts code patterns from PR changes
- **AI Code Generation**: Uses OpenAI GPT-4 to generate similar code based on extracted patterns
- **REST API**: Simple REST endpoints for integration

## Prerequisites

- Java 17 or higher
- Maven 3.6 or higher
- GitHub Personal Access Token
- OpenAI API Key

## Setup

### 1. Configure API Keys

Create environment variables:

```bash
export GITHUB_TOKEN=your-github-personal-access-token
export OPENAI_API_KEY=your-openai-api-key
```

Or update the `src/main/resources/application.properties` file:
```properties
github.token=your-github-personal-access-token
openai.api.key=your-openai-api-key
```

### 2. Build the Application
```bash
mvn clean compile
```

### 3. Run Tests
```bash
mvn test
```

### 4. Start the Application
```bash
mvn spring-boot:run
```

The application will start on `http://localhost:8080`

## API Usage

### Analyze PR and Generate Code

**POST** `/api/pr-analyzer/analyze-and-generate`

**Request Body:**
```json
{
  "prUrl": "https://github.com/owner/repo/pull/123",
  "requirements": "Create a similar function for user management"
}
```

**Response:**
```json
{
  "prAnalysis": {
    "title": "Add user authentication",
    "description": "This PR adds user authentication functionality",
    "files": [...]
  },
  "extractedPatterns": {
    "methodPatterns": ["public void authenticate(String username, String password)"],
    "variablePatterns": ["String username", "boolean isAuthenticated"],
    "logicPatterns": ["if (username != null && password != null)"],
    "structuralPatterns": ["new UserService()"]
  },
  "generatedCode": "// Generated code based on patterns...",
  "success": true
}
```

### Health Check

**GET** `/api/pr-analyzer/health`

Returns: `"PR Analyzer is running"`

## Project Structure

```
src/
├── main/
│   ├── java/com/reflexai/pranalyzer/
│   │   ├── controller/          # REST controllers
│   │   ├── service/             # Business logic services
│   │   ├── model/               # Data models
│   │   ├── util/                # Utility classes
│   │   └── Application.java     # Main application class
│   └── resources/
│       └── application.properties
└── test/
    └── java/                    # Test classes
```

## How It Works

1. **PR Analysis**: Fetches PR data from GitHub API including file changes and diffs
2. **Pattern Extraction**: Analyzes code changes to extract patterns like:
   - Method signatures and naming conventions
   - Variable declarations and types
   - Conditional logic patterns
   - Structural patterns (class usage, inheritance)
3. **Code Generation**: Sends extracted patterns to OpenAI GPT-4 with user requirements to generate similar code

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure your GitHub token has proper permissions
2. **OpenAI API Errors**: Check your API key and rate limits
3. **Build Errors**: Ensure Java 17 and Maven are properly installed

## License

This project is licensed under the MIT License.
