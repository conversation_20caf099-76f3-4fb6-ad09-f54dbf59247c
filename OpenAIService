@Service
public class OpenAIService {
    
    @Value("${openai.api.key}")
    private String apiKey;
    
    private final RestTemplate restTemplate;
    
    public OpenAIService() {
        this.restTemplate = new RestTemplate();
    }
    
    public String generateSimilarCode(CodePatterns patterns, String requirements) {
        String prompt = buildPrompt(patterns, requirements);
        
        OpenAIRequest request = OpenAIRequest.builder()
            .model("gpt-4")
            .messages(Arrays.asList(
                new OpenAIMessage("system", "You are a code generation assistant that follows established patterns."),
                new OpenAIMessage("user", prompt)
            ))
            .temperature(0.1)
            .maxTokens(2000)
            .build();
            
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + apiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<OpenAIRequest> entity = new HttpEntity<>(request, headers);
        
        ResponseEntity<OpenAIResponse> response = restTemplate.exchange(
            "https://api.openai.com/v1/chat/completions", 
            HttpMethod.POST, entity, OpenAIResponse.class);
            
        return response.getBody().getChoices().get(0).getMessage().getContent();
    }
    
    private String buildPrompt(CodePatterns patterns, String requirements) {
        return String.format("""
            Based on these patterns from a previous PR:
            
            Method Patterns:
            %s
            
            Variable Patterns:
            %s
            
            Logic Patterns:
            %s
            
            Structural Patterns:
            %s
            
            Generate similar code for: %s
            
            Follow the same naming conventions, code structure, and business logic patterns.
            """, 
            String.join("\n", patterns.getMethodPatterns()),
            String.join("\n", patterns.getVariablePatterns()),
            String.join("\n", patterns.getLogicPatterns()),
            String.join("\n", patterns.getStructuralPatterns()),
            requirements);
    }
}